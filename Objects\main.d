.\objects\main.o: user\main.c
.\objects\main.o: user\main.h
.\objects\main.o: .\CMSIS\gd32f4xx.h
.\objects\main.o: .\CMSIS\core_cm4.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\main.o: .\CMSIS\core_cmInstr.h
.\objects\main.o: .\CMSIS\core_cmFunc.h
.\objects\main.o: .\CMSIS\core_cm4_simd.h
.\objects\main.o: .\CMSIS\system_gd32f4xx.h
.\objects\main.o: .\CMSIS\gd32f4xx_libopt.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_rcu.h
.\objects\main.o: .\CMSIS\gd32f4xx.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_adc.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_can.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_crc.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_ctc.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_dac.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_dbg.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_dci.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_dma.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_exti.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_fmc.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_fwdgt.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_gpio.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_syscfg.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_i2c.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_iref.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_pmu.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_rtc.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_sdio.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_spi.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_timer.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_trng.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_usart.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_wwdgt.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_misc.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_enet.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_exmc.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_ipa.h
.\objects\main.o: .\FWLib\Include\gd32f4xx_tli.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\main.o: .\user\FreeRTOSConfig.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\include\projdefs.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\include\portable.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\include\task.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\include\list.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h
.\objects\main.o: .\user\FreeRTOSIPConfig.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\include\queue.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\include\semphr.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\include\event_groups.h
.\objects\main.o: .\Middlewares\FreeRTOS_Kernel\include\timers.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h
.\objects\main.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h
.\objects\main.o: user\uart0.h
.\objects\main.o: user\main.h
.\objects\main.o: user\trng.h
