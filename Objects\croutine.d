.\objects\croutine.o: Middlewares\FreeRTOS_Kernel\croutine.c
.\objects\croutine.o: .\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h
.\objects\croutine.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\croutine.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\croutine.o: .\user\FreeRTOSConfig.h
.\objects\croutine.o: .\CMSIS\gd32f4xx.h
.\objects\croutine.o: .\CMSIS\core_cm4.h
.\objects\croutine.o: .\CMSIS\core_cmInstr.h
.\objects\croutine.o: .\CMSIS\core_cmFunc.h
.\objects\croutine.o: .\CMSIS\core_cm4_simd.h
.\objects\croutine.o: .\CMSIS\system_gd32f4xx.h
.\objects\croutine.o: .\CMSIS\gd32f4xx_libopt.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_rcu.h
.\objects\croutine.o: .\CMSIS\gd32f4xx.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_adc.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_can.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_crc.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_ctc.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_dac.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_dbg.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_dci.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_dma.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_exti.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_fmc.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_fwdgt.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_gpio.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_syscfg.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_i2c.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_iref.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_pmu.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_rtc.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_sdio.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_spi.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_timer.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_trng.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_usart.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_wwdgt.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_misc.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_enet.h
.\objects\croutine.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_exmc.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_ipa.h
.\objects\croutine.o: .\FWLib\Include\gd32f4xx_tli.h
.\objects\croutine.o: .\Middlewares\FreeRTOS_Kernel\include\projdefs.h
.\objects\croutine.o: .\Middlewares\FreeRTOS_Kernel\include\portable.h
.\objects\croutine.o: .\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h
.\objects\croutine.o: .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\croutine.o: .\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h
.\objects\croutine.o: .\Middlewares\FreeRTOS_Kernel\include\task.h
.\objects\croutine.o: .\Middlewares\FreeRTOS_Kernel\include\list.h
.\objects\croutine.o: .\Middlewares\FreeRTOS_Kernel\include\croutine.h
