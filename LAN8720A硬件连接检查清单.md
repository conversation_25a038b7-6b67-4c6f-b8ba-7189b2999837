# LAN8720A 硬件连接问题诊断清单

## 🚨 当前问题
PHY寄存器读取返回 `0xFFFF`，表明MDIO通信失败。

## 🔍 硬件连接检查清单

### 1. 电源连接 ⚡
- [ ] **VDD33A (3.3V模拟电源)** - 引脚9
- [ ] **VDD33IO (3.3V数字I/O电源)** - 引脚17
- [ ] **VDDCR (1.2V内核电源)** - 引脚5，需要外部1.2V或内部LDO
- [ ] **所有GND引脚** - 引脚4, 10, 15, 21, 27
- [ ] **电源去耦电容** - 每个电源引脚附近需要0.1μF + 10μF

### 2. 时钟连接 🕐
- [ ] **外部50MHz时钟** → **XTAL1/CLKIN (引脚7)**
- [ ] **外部50MHz时钟** → **GD32F4xx PA1 (RMII_REF_CLK)**
- [ ] **XTAL2 (引脚8)** - 悬空或接地
- [ ] **时钟信号质量** - 上升/下降时间 < 2ns，占空比 45-55%

### 3. RMII接口连接 🔌
| 信号 | LAN8720A引脚 | GD32F4xx引脚 | 方向 | 检查状态 |
|------|-------------|-------------|------|----------|
| RMII_REF_CLK | 引脚16 | PA1 | 输入 | [ ] |
| RMII_MDIO | 引脚18 | PA2 | 双向 | [ ] |
| RMII_CRS_DV | 引脚25 | PA7 | 输入 | [ ] |
| RMII_TX_EN | 引脚21 | PB11 | 输出 | [ ] |
| RMII_TXD0 | 引脚22 | PB12 | 输出 | [ ] |
| RMII_TXD1 | 引脚23 | PB13 | 输出 | [ ] |
| RMII_MDC | 引脚19 | PC1 | 输出 | [ ] |
| RMII_RXD0 | 引脚26 | PC4 | 输入 | [ ] |
| RMII_RXD1 | 引脚24 | PC5 | 输入 | [ ] |

### 4. 控制信号连接 🎛️
- [ ] **nRST (引脚6)** - 复位信号，高电平有效，需要上拉电阻(10kΩ)
- [ ] **nINT (引脚12)** - 中断信号（可选）
- [ ] **LED1 (引脚13)** - 链路状态LED（可选）
- [ ] **LED2 (引脚14)** - 活动状态LED（可选）

### 5. 配置引脚 ⚙️
- [ ] **MODE0 (引脚1)** - 配置为RMII模式
- [ ] **MODE1 (引脚2)** - 配置为RMII模式  
- [ ] **MODE2 (引脚3)** - 配置为RMII模式
- [ ] **PHYAD0 (引脚11)** - PHY地址位0

**RMII模式配置：**
- MODE[2:0] = 001 (MODE2=0, MODE1=0, MODE0=1)
- PHYAD0 = 0 (PHY地址 = 0)

### 6. 网络接口连接 🌐
- [ ] **RXN (引脚28)** - 接收差分负信号
- [ ] **RXP (引脚1)** - 接收差分正信号  
- [ ] **TXN (引脚20)** - 发送差分负信号
- [ ] **TXP (引脚19)** - 发送差分正信号
- [ ] **网络变压器** - 隔离和阻抗匹配
- [ ] **RJ45连接器** - 带LED指示

## 🔧 软件调试步骤

### 1. 编译并下载新的调试代码
新代码包含详细的硬件检查功能。

### 2. 查看串口输出
检查以下信息：
- 硬件连接检查结果
- PHY地址扫描结果
- 寄存器配置状态

### 3. 预期的正常输出
```
=== Hardware Connection Check ===
ENET clock: ENABLED
ENET TX clock: ENABLED  
ENET RX clock: ENABLED
PHY interface: RMII
=== End Hardware Check ===

=== PHY Status Check ===
Trying PHY address 0...
PHY found at address 0, ID1: 0x0007
PHY ID2: 0xC0F1
LAN8720A detected!
```

## ⚠️ 常见问题和解决方案

### 问题1: 所有寄存器读取返回0xFFFF
**原因：** MDIO通信失败
**检查：**
- MDIO/MDC引脚连接
- PHY芯片电源
- 时钟信号

### 问题2: PHY地址扫描无响应
**原因：** PHY芯片未正确配置或损坏
**检查：**
- MODE[2:0]引脚配置
- PHYAD0引脚配置
- 复位信号nRST

### 问题3: 时钟相关问题
**原因：** 50MHz时钟信号问题
**检查：**
- 时钟源输出
- 时钟信号完整性
- 时钟负载能力

## 📋 下一步行动

1. **立即检查：**
   - [ ] 确认LAN8720A芯片电源电压
   - [ ] 用万用表测量50MHz时钟信号
   - [ ] 检查MDIO/MDC引脚连接

2. **如果硬件正常：**
   - [ ] 尝试不同的PHY地址配置
   - [ ] 检查PCB布线和信号完整性
   - [ ] 考虑更换LAN8720A芯片

3. **软件调试：**
   - [ ] 运行新的调试代码
   - [ ] 分析硬件检查输出
   - [ ] 根据结果调整配置

## 📞 技术支持

如果问题持续存在，请提供：
1. 完整的串口调试输出
2. 硬件连接示意图
3. PCB布线图（如果可能）
4. 电源测量结果
