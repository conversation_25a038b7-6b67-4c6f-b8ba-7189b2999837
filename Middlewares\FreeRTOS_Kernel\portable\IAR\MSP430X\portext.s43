/*
 * FreeRTOS Kernel V10.5.1
 * Copyright (C) 2021 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
 *
 * SPDX-License-Identifier: MIT
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 * https://www.FreeRTOS.org
 * https://github.com/FreeRTOS
 *
 */
#include "msp430.h"
#include "FreeRTOSConfig.h"
#include "data_model.h"

	IMPORT xTaskIncrementTick
	IMPORT vTaskSwitchContext
	IMPORT vPortSetupTimerInterrupt
	IMPORT pxCurrentTCB
	IMPORT usCriticalNesting

	EXPORT vPortTickISR
	EXPORT vPortYield
	EXPORT xPortStartScheduler

portSAVE_CONTEXT macro

	/* Save the remaining registers. */
	pushm_x	#12, r15
	mov.w	&usCriticalNesting, r14
	push_x r14
	mov_x	&pxCurrentTCB, r12
	mov_x	sp, 0( r12 )
	endm
/*-----------------------------------------------------------*/

portRESTORE_CONTEXT macro

	mov_x	&pxCurrentTCB, r12
	mov_x	@r12, sp
	pop_x	r15
	mov.w	r15, &usCriticalNesting
	popm_x	#12, r15
	nop
	pop.w	sr
	nop
	reta
	endm
/*-----------------------------------------------------------*/


/*
 * The RTOS tick ISR.
 *
 * If the cooperative scheduler is in use this simply increments the tick
 * count.
 *
 * If the preemptive scheduler is in use a context switch can also occur.
 */

	RSEG CODE
	EVEN

vPortTickISR:

	/* The sr is not saved in portSAVE_CONTEXT() because vPortYield() needs
	to save it manually before it gets modified (interrupts get disabled).
	Entering through this interrupt means the SR is already on the stack, but
	this keeps the stack frames identical. */
	push.w sr
	portSAVE_CONTEXT

	calla	#xTaskIncrementTick
	cmp.w   #0x0, R12
    jeq     SkipContextSwitch
    calla   #vTaskSwitchContext
SkipContextSwitch:
	portRESTORE_CONTEXT
/*-----------------------------------------------------------*/

/*
 * Manual context switch called by the portYIELD() macro.
 */
 	EVEN

vPortYield:

	/* The sr needs saving before it is modified. */
	push.w	sr

	/* Now the SR is stacked interrupts can be disabled. */
	dint
	nop

	/* Save the context of the current task. */
	portSAVE_CONTEXT

	/* Select the next task to run. */
	calla	#vTaskSwitchContext

	/* Restore the context of the new task. */
	portRESTORE_CONTEXT
/*-----------------------------------------------------------*/


/*
 * Start off the scheduler by initialising the RTOS tick timer, then restoring
 * the context of the first task.
 */
 	EVEN

xPortStartScheduler:

	/* Setup the hardware to generate the tick.  Interrupts are disabled
	when this function is called. */
	calla	#vPortSetupTimerInterrupt

	/* Restore the context of the first task that is going to run. */
	portRESTORE_CONTEXT
/*-----------------------------------------------------------*/

	END

