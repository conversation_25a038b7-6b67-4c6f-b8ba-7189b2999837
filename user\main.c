#include "main.h"
#include "trng.h"
uint8_t ucMACAddress[ 6 ] = { configMAC_ADDR0, configMAC_ADDR1, configMAC_ADDR2, configMAC_ADDR3, configMAC_ADDR4, configMAC_ADDR5 };
uint8_t ucIPAddress[ 4 ] = { configIP_ADDR0, configIP_ADDR1, configIP_ADDR2, configIP_ADDR3 }; 
uint8_t ucNetMask[ 4 ] = { configNET_MASK0, configNET_MASK1, configNET_MASK2, configNET_MASK3 }; 
uint8_t ucGatewayAddress[ 4 ] = { configGATEWAY_ADDR0, configGATEWAY_ADDR1, configGATEWAY_ADDR2, configGATEWAY_ADDR3 }; 
uint8_t ucDNSServerAddress[ 4 ] = { configDNS_SERVER_ADDR0, configDNS_SERVER_ADDR1, configDNS_SERVER_ADDR2, configDNS_SERVER_ADDR3 }; 

void start_task(void * pvParameters)
{
    printf("hello\r\n");
	while (1)
	{
		
		vTaskDelay(1000);
	}
}
void enet_inti_task(void * pvParameters)
{
    trng_init();         //初始化随机数发生器,在FreeRTOS_IPInit之前
    FreeRTOS_IPInit( ucIPAddress, ucNetMask, ucGatewayAddress, ucDNSServerAddress, ucMACAddress ); /* 初始化网络栈*/
	vTaskDelete(NULL);
}

int main(void)
{
	nvic_priority_group_set(NVIC_PRIGROUP_PRE4_SUB0);
	uart0_init(115200);
	xTaskCreate(enet_inti_task , "enet_inti_task" , 128, NULL , 10 , NULL);
	xTaskCreate(start_task , "start_task" , 128, NULL , 10 , NULL);
	vTaskStartScheduler();			
	while(1)
	{
		printf("freertos error\r\n");
	}
}
