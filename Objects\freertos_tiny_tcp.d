.\objects\freertos_tiny_tcp.o: Middlewares\FreeRTOS_TCP\FreeRTOS_Tiny_TCP.c
.\objects\freertos_tiny_tcp.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h
.\objects\freertos_tiny_tcp.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\freertos_tiny_tcp.o: .\user\FreeRTOSConfig.h
.\objects\freertos_tiny_tcp.o: .\CMSIS\gd32f4xx.h
.\objects\freertos_tiny_tcp.o: .\CMSIS\core_cm4.h
.\objects\freertos_tiny_tcp.o: .\CMSIS\core_cmInstr.h
.\objects\freertos_tiny_tcp.o: .\CMSIS\core_cmFunc.h
.\objects\freertos_tiny_tcp.o: .\CMSIS\core_cm4_simd.h
.\objects\freertos_tiny_tcp.o: .\CMSIS\system_gd32f4xx.h
.\objects\freertos_tiny_tcp.o: .\CMSIS\gd32f4xx_libopt.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_rcu.h
.\objects\freertos_tiny_tcp.o: .\CMSIS\gd32f4xx.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_adc.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_can.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_crc.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_ctc.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_dac.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_dbg.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_dci.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_dma.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_exti.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_fmc.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_fwdgt.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_gpio.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_syscfg.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_i2c.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_iref.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_pmu.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_rtc.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_sdio.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_spi.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_timer.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_trng.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_usart.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_wwdgt.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_misc.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_enet.h
.\objects\freertos_tiny_tcp.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_exmc.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_ipa.h
.\objects\freertos_tiny_tcp.o: .\FWLib\Include\gd32f4xx_tli.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\include\projdefs.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\include\portable.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\include\task.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\include\list.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h
.\objects\freertos_tiny_tcp.o: .\user\FreeRTOSIPConfig.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h
.\objects\freertos_tiny_tcp.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\freertos_tiny_tcp.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\include\queue.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\include\semphr.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\include\event_groups.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_Kernel\include\timers.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h
.\objects\freertos_tiny_tcp.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h
