.\objects\heap_4.o: Middlewares\FreeRTOS_Kernel\portable\MemMang\heap_4.c
.\objects\heap_4.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\heap_4.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
.\objects\heap_4.o: .\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h
.\objects\heap_4.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\heap_4.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\heap_4.o: .\user\FreeRTOSConfig.h
.\objects\heap_4.o: .\CMSIS\gd32f4xx.h
.\objects\heap_4.o: .\CMSIS\core_cm4.h
.\objects\heap_4.o: .\CMSIS\core_cmInstr.h
.\objects\heap_4.o: .\CMSIS\core_cmFunc.h
.\objects\heap_4.o: .\CMSIS\core_cm4_simd.h
.\objects\heap_4.o: .\CMSIS\system_gd32f4xx.h
.\objects\heap_4.o: .\CMSIS\gd32f4xx_libopt.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_rcu.h
.\objects\heap_4.o: .\CMSIS\gd32f4xx.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_adc.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_can.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_crc.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_ctc.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_dac.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_dbg.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_dci.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_dma.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_exti.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_fmc.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_fwdgt.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_gpio.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_syscfg.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_i2c.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_iref.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_pmu.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_rtc.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_sdio.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_spi.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_timer.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_trng.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_usart.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_wwdgt.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_misc.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_enet.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_exmc.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_ipa.h
.\objects\heap_4.o: .\FWLib\Include\gd32f4xx_tli.h
.\objects\heap_4.o: .\Middlewares\FreeRTOS_Kernel\include\projdefs.h
.\objects\heap_4.o: .\Middlewares\FreeRTOS_Kernel\include\portable.h
.\objects\heap_4.o: .\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h
.\objects\heap_4.o: .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\heap_4.o: .\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h
.\objects\heap_4.o: .\Middlewares\FreeRTOS_Kernel\include\task.h
.\objects\heap_4.o: .\Middlewares\FreeRTOS_Kernel\include\list.h
