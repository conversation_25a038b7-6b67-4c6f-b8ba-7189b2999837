#ifndef _ENET_H_
#define _ENET_H_
#include "main.h"
/* 设置以太网系统(GPIOs, clocks, MAC, DMA, systick) */
ErrStatus InitialiseNetwork(void);

typedef enum
{
	RMII_REF_CLK_GPIO_NUM=GPIOA,
	RMII_MDIO_GPIO_NUM   =GPIOA,
	RMII_CRS_DV_GPIO_NUM =GPIOA,
	RMII_TX_EN_GPIO_NUM  =GPIOB,
	RMII_TXD0_GPIO_NUM   =GPIOB,
	RMII_TXD1_GPIO_NUM   =GPIOB,
	RMII_MDC_GPIO_NUM    =GPIOC,
	RMII_RXD0_GPIO_NUM   =GPIOC,
	RMII_RXD1_GPIO_NUM   =GPIOC
	
}RMII_GPIO_NUM;

typedef enum
{
	RMII_REF_CLK_PIN =GPIO_PIN_1,
	RMII_MDIO_PIN    =GPIO_PIN_2,
	RMII_CRS_DV_PIN  =GPIO_PIN_7,
	RMII_TX_EN_PIN   =GPIO_PIN_11,
	RMII_TXD0_PIN    =GPIO_PIN_12,
	RMII_TXD1_PIN    =GPIO_PIN_13,
	RMII_MDC_PIN     =GPIO_PIN_1,
	RMII_RXD0_PIN    =GPIO_PIN_4,
	RMII_RXD1_PIN    =GPIO_PIN_5
	
}RMII_GPIO_PIN;

#define CK_OUT0_GPIO_NUM            GPIOA
#define CK_OUT0_PIN                 GPIO_PIN_8

#endif



