#ifndef _ENET_H_
#define _ENET_H_
#include "main.h"

/*
 * 以太网时钟配置说明：
 * 本项目使用外部50MHz时钟源，同时为LAN8720A PHY和RMII接口提供时钟
 *
 * 硬件连接：
 * - 外部50MHz时钟 -> LAN8720A XTAL1(CLKIN)
 * - 外部50MHz时钟 -> GD32F4xx PA1(RMII_REF_CLK)
 *
 * 注意：不使用MCU的CK_OUT0输出时钟
 */

/* 设置以太网系统(GPIOs, clocks, MAC, DMA, systick) */
ErrStatus InitialiseNetwork(void);

typedef enum
{
	RMII_REF_CLK_GPIO_NUM=GPIOA,
	RMII_MDIO_GPIO_NUM   =GPIOA,
	RMII_CRS_DV_GPIO_NUM =GPIOA,
	RMII_TX_EN_GPIO_NUM  =GPIOB,
	RMII_TXD0_GPIO_NUM   =GPIOB,
	RMII_TXD1_GPIO_NUM   =GPIOB,
	RMII_MDC_GPIO_NUM    =GPIOC,
	RMII_RXD0_GPIO_NUM   =GPIOC,
	RMII_RXD1_GPIO_NUM   =GPIOC
	
}RMII_GPIO_NUM;

typedef enum
{
	RMII_REF_CLK_PIN =GPIO_PIN_1,
	RMII_MDIO_PIN    =GPIO_PIN_2,
	RMII_CRS_DV_PIN  =GPIO_PIN_7,
	RMII_TX_EN_PIN   =GPIO_PIN_11,
	RMII_TXD0_PIN    =GPIO_PIN_12,
	RMII_TXD1_PIN    =GPIO_PIN_13,
	RMII_MDC_PIN     =GPIO_PIN_1,
	RMII_RXD0_PIN    =GPIO_PIN_4,
	RMII_RXD1_PIN    =GPIO_PIN_5
	
}RMII_GPIO_PIN;

#define CK_OUT0_GPIO_NUM            GPIOA
#define CK_OUT0_PIN                 GPIO_PIN_8

#endif



