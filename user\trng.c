#include "trng.h"
#include "main.h"


ErrStatus trng_ready_check(void)
{
    uint32_t timeout = 0;
    FlagStatus trng_flag = RESET;
    ErrStatus reval = SUCCESS;
    
    /* check wherther the random data is valid */
    do{
        timeout++;
        trng_flag = trng_flag_get(TRNG_FLAG_DRDY);
    }while((RESET == trng_flag) &&(0xFFFF > timeout));
    
    if(RESET == trng_flag)
    {   
        /* ready check timeout */
        printf("Error: TRNG can't ready \r\n");
        trng_flag = trng_flag_get(TRNG_FLAG_CECS);
        printf("Clock error current status: %d \r\n", trng_flag);
        trng_flag = trng_flag_get(TRNG_FLAG_SECS);
        printf("Seed error current status: %d \r\n", trng_flag);  
        reval = ERROR;
    }
    
    /* return check status */
    return reval;
}

static ErrStatus trng_config(void)
{
    ErrStatus reval = SUCCESS;
    
    /* TRNG module clock enable */
    rcu_periph_clock_enable(RCU_TRNG);
    
    /* TRNG registers reset */
    trng_deinit();
    trng_enable();
    /* check TRNG work status */
    reval = trng_ready_check();
    return reval;
}



void trng_init(void)
{
    uint8_t retry = 0;
    while((ERROR == trng_config()) && retry < 3)
    {
        printf("TRNG init fail \r\n");
        printf("TRNG init retry \r\n");
        retry++;
    }

}



//获取32位随机数
uint32_t trng_random_range_get(void)
{
    if(SUCCESS == trng_ready_check()) {
        return abs(trng_get_true_random_data())   ;
    } else {
        return 0;
    }
}



