<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Target 1</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::.\ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>GD32F450VG</Device>
          <Vendor>GigaDevice</Vendor>
          <PackID>GigaDevice.GD32F4xx_DFP.3.0.3</PackID>
          <PackURL>https://gd32mcu.com/data/documents/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x030000) IRAM2(0x10000000,0x010000) IROM(0x08000000,0x0100000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0GD32F4xx_1MB -********** -********* -FP0($$Device:GD32F450VG$Flash\GD32F4xx_1MB.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:GD32F450VG$Device\F4XX\Include\gd32f4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:GD32F450VG$SVD\GD32F4xx.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>project</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x30000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x30000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>1</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_STDPERIPH_DRIVER,GD32F450</Define>
              <Undefine></Undefine>
              <IncludePath>.\CMSIS;.\FWLib\Include;.\Middlewares\FreeRTOS_Kernel\include;.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F;.\user;.\Middlewares\FreeRTOS_TCP\include;.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>gd32f4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\CMSIS\gd32f4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>startup_gd32f450_470.s</FileName>
              <FileType>2</FileType>
              <FilePath>.\CMSIS\startup_gd32f450_470.s</FilePath>
            </File>
            <File>
              <FileName>system_gd32f4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\CMSIS\system_gd32f4xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FWLib</GroupName>
          <Files>
            <File>
              <FileName>gd32f4xx_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_adc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_can.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_can.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_crc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_ctc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_ctc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_dac.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_dbg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_dbg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_dci.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_dci.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_dma.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_enet.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_enet.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_exmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_exmc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_exti.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_fmc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_fwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_fwdgt.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_gpio.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_i2c.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_ipa.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_ipa.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_iref.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_iref.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_misc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_pmu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_rcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_rcu.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_rtc.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_sdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_sdio.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_spi.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_syscfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_syscfg.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_timer.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_tli.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_tli.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_trng.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_usart.c</FilePath>
            </File>
            <File>
              <FileName>gd32f4xx_wwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\FWLib\Source\gd32f4xx_wwdgt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FreeRTOS_Kernel</GroupName>
          <Files>
            <File>
              <FileName>croutine.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_Kernel\croutine.c</FilePath>
            </File>
            <File>
              <FileName>event_groups.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_Kernel\event_groups.c</FilePath>
            </File>
            <File>
              <FileName>list.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_Kernel\list.c</FilePath>
            </File>
            <File>
              <FileName>queue.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_Kernel\queue.c</FilePath>
            </File>
            <File>
              <FileName>stream_buffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_Kernel\stream_buffer.c</FilePath>
            </File>
            <File>
              <FileName>tasks.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_Kernel\tasks.c</FilePath>
            </File>
            <File>
              <FileName>timers.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_Kernel\timers.c</FilePath>
            </File>
            <File>
              <FileName>heap_4.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_Kernel\portable\MemMang\heap_4.c</FilePath>
            </File>
            <File>
              <FileName>port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\port.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FreeRTOS_TCP</GroupName>
          <Files>
            <File>
              <FileName>FreeRTOS_ARP.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_ARP.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_DHCP.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_DHCP.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_DNS.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_DNS.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_DNS_Cache.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Cache.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_DNS_Callback.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Callback.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_DNS_Networking.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Networking.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_DNS_Parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Parser.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_ICMP.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_ICMP.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_IP.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_IP.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_IP_Timers.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_IP_Timers.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_IP_Utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_IP_Utils.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_Sockets.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_Sockets.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_Stream_Buffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_Stream_Buffer.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_TCP_IP.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_IP.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_TCP_Reception.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_Reception.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_TCP_State_Handling.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_State_Handling.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_TCP_Transmission.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_Transmission.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_TCP_Utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_Utils.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_TCP_WIN.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_WIN.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_Tiny_TCP.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_Tiny_TCP.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOS_UDP_IP.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\FreeRTOS_UDP_IP.c</FilePath>
            </File>
            <File>
              <FileName>BufferAllocation_2.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\portable\BufferManagement\BufferAllocation_2.c</FilePath>
            </File>
            <File>
              <FileName>NetworkInterface.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Middlewares\FreeRTOS_TCP\portable\NetworkInterface\NetworkInterface.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\main.c</FilePath>
            </File>
            <File>
              <FileName>uart0.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\uart0.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOSConfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\user\FreeRTOSConfig.h</FilePath>
            </File>
            <File>
              <FileName>FreeRTOSIPConfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\user\FreeRTOSIPConfig.h</FilePath>
            </File>
            <File>
              <FileName>enet.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\enet.c</FilePath>
            </File>
            <File>
              <FileName>trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\trng.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>&lt;Project Info&gt;</LayName>
        <LayTarg>0</LayTarg>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
