
OVERVIEW

This directory contains FreeRTOS port for Texas Instruments C28x based microcontrollers.

The standard demo project to test this port is added at following location:
FreeRTOS-Community-Supported-Demos\C2000_F2838x_C28x_CCS\freertos_ex1_c28x_port_val

This port is distributed under MIT open source license.

KNOWN ISSUES:
Support for "fpu64" is not added yet to the port. The examples should specify "fpu32" as option for floating point.

TOOL CHAIN SUPPORT:
Code Composer Studio™ IDE (CCS) v11.1.0 or newer
C2000 Compiler v20.2.1.LTS or newer
C2000Ware_3_01_00_00 or newer
FreeRTOSv202112.00

