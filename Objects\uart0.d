.\objects\uart0.o: user\uart0.c
.\objects\uart0.o: user\uart0.h
.\objects\uart0.o: user\main.h
.\objects\uart0.o: .\CMSIS\gd32f4xx.h
.\objects\uart0.o: .\CMSIS\core_cm4.h
.\objects\uart0.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\uart0.o: .\CMSIS\core_cmInstr.h
.\objects\uart0.o: .\CMSIS\core_cmFunc.h
.\objects\uart0.o: .\CMSIS\core_cm4_simd.h
.\objects\uart0.o: .\CMSIS\system_gd32f4xx.h
.\objects\uart0.o: .\CMSIS\gd32f4xx_libopt.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_rcu.h
.\objects\uart0.o: .\CMSIS\gd32f4xx.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_adc.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_can.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_crc.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_ctc.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_dac.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_dbg.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_dci.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_dma.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_exti.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_fmc.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_fwdgt.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_gpio.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_syscfg.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_i2c.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_iref.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_pmu.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_rtc.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_sdio.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_spi.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_timer.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_trng.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_usart.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_wwdgt.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_misc.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_enet.h
.\objects\uart0.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_exmc.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_ipa.h
.\objects\uart0.o: .\FWLib\Include\gd32f4xx_tli.h
.\objects\uart0.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h
.\objects\uart0.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\uart0.o: .\user\FreeRTOSConfig.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\include\projdefs.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\include\portable.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\include\task.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\include\list.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h
.\objects\uart0.o: .\user\FreeRTOSIPConfig.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h
.\objects\uart0.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\include\queue.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\include\semphr.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\include\event_groups.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_Kernel\include\timers.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h
.\objects\uart0.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h
.\objects\uart0.o: user\uart0.h
