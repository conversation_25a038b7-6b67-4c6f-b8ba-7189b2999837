#include "enet.h"
#include "gd32f4xx_enet.h"

#define configMAC_ADDR0		2
#define configMAC_ADDR1		0xA
#define configMAC_ADDR2		0xC
#define configMAC_ADDR3		0x5
#define configMAC_ADDR4		0x6
#define configMAC_ADDR5		0x6

extern enet_descriptors_struct	txdesc_tab[ENET_TXBUF_NUM];/*ENET TxDMA������, ��ַ�ᱻ����DMX_RDTADDR�Ĵ�����*/
extern enet_descriptors_struct  rxdesc_tab[ENET_RXBUF_NUM];/*ENET RxDMA����������ַ�ᱻ����DMX_TDTADDR�Ĵ�����*/

static void eth_rmii_gpio_conifg(RMII_GPIO_NUM gpio_periph,RMII_GPIO_PIN pin);
static void enet_gpio_config(void);
static void enet_external_clock_config(void);


/*������̫��ϵͳ(GPIOs, clocks, MAC, DMA, systick)*/
ErrStatus InitialiseNetwork(void)
{
    ErrStatus flag = ERROR;
    /*����Ƕ��ʸ���жϿ�����*/
    nvic_irq_enable(ENET_IRQn, 6 , 0);
    
    enet_gpio_config();                             /* ������̫�����ŵ�GPIO�˿� */
 
    /* ������̫��ʱ�� */
    rcu_periph_clock_enable(RCU_ENET);
    rcu_periph_clock_enable(RCU_ENETTX);
    rcu_periph_clock_enable(RCU_ENETRX);

    enet_deinit();  /* ��AHB������������̫�� */

    enet_software_reset(); //����λ��CLK_TX��CLK_RX�����к����ڲ��Ĵ���

    /*��Ӳ���������֤IP��UDP��TCP��ICMP��У���*/
    if (enet_init(ENET_AUTO_NEGOTIATION, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS))
    {
        flag = SUCCESS;
    }
	
    enet_interrupt_enable(ENET_DMA_INT_NIE);        //ʹ��ENET MAC/MSC/DMA�жϣ������жϻ�������
    enet_interrupt_enable(ENET_DMA_INT_RIE);        //�����ж�����
    enet_mac_address_set(ENET_MAC_ADDRESS0,ucMACAddress);//д��mac��ַ	

    enet_descriptors_chain_init(ENET_DMA_TX);//��ʼ��DMA����/����������Ϊ��ģʽ
    enet_descriptors_chain_init(ENET_DMA_RX); 
    
    for(uint8_t i=0; i<ENET_RXBUF_NUM; i++)//���������ʱ��������λENET_DMA_STAT�Ĵ�����RSλ
        enet_rx_desc_immediate_receive_complete_interrupt(&rxdesc_tab[i]);
    
    for(uint8_t i=0; i < ENET_TXBUF_NUM; i++)
        enet_transmit_checksum_config(&txdesc_tab[i], ENET_CHECKSUM_TCPUDPICMP_FULL);
    
    enet_enable();           	 //ENET Tx/Rx����ʹ�ܣ�����ENET�����ڵ�MAC��DMAģ�飩 

    return flag;
}
 /*Э��ջ��ʼ���ɹ������� vApplicationIPNetworkEventHook() �����е�IP_task�������洴��  ethernet_task_creation �������洴��*/


/*����RMII�˿�*/
static void enet_gpio_config(void)
{
    rcu_periph_clock_enable(RCU_GPIOA);
    rcu_periph_clock_enable(RCU_GPIOB);
    rcu_periph_clock_enable(RCU_GPIOC);
	
	/*�ⲿ50MHzʱ������ - ����ҪMCU���ʱ�ӵ�PHY*/
    /* �ⲿʱ��ֱ�����ӵ�LAN8720A��XTAL1(CLKIN)��RMII_REF_CLK */
    /* ע�͵�MCUʱ��������ã���Ϊʹ���ⲿ50MHzʱ��Դ */

    // gpio_af_set(CK_OUT0_GPIO_NUM, GPIO_AF_0, CK_OUT0_PIN);
    // gpio_mode_set(CK_OUT0_GPIO_NUM, GPIO_MODE_AF, GPIO_PUPD_NONE, CK_OUT0_PIN);
    // gpio_output_options_set(CK_OUT0_GPIO_NUM, GPIO_OTYPE_PP, GPIO_OSPEED_MAX, CK_OUT0_PIN);
    // rcu_ckout0_config(RCU_CKOUT0SRC_PLLP, RCU_CKOUT0_DIV4);//50M - ����Ҫ
    // rcu_ckout0_config(RCU_CKOUT0SRC_HXTAL, RCU_CKOUT0_DIV1);//25M - ����Ҫ
    
	rcu_periph_clock_enable(RCU_SYSCFG);    							/* ʹ��SYSCFGʱ�� */
	syscfg_enet_phy_interface_config(SYSCFG_ENET_PHY_RMII);//������̫��MAC��PHY�ӿ�

	/* �����ⲿ50MHzʱ�� */
	enet_external_clock_config();

    /* RMII_REF_CLK (PA1) ����Ϊ����ģʽ�������ⲿ50MHzʱ�� */
    gpio_mode_set(RMII_REF_CLK_GPIO_NUM, GPIO_MODE_AF, GPIO_PUPD_NONE, RMII_REF_CLK_PIN);
    gpio_af_set(RMII_REF_CLK_GPIO_NUM, GPIO_AF_11, RMII_REF_CLK_PIN);
	eth_rmii_gpio_conifg(RMII_MDIO_GPIO_NUM     , RMII_MDIO_PIN);
	eth_rmii_gpio_conifg(RMII_CRS_DV_GPIO_NUM   , RMII_CRS_DV_PIN);
	
	eth_rmii_gpio_conifg(RMII_TX_EN_GPIO_NUM    , RMII_TX_EN_PIN);
	eth_rmii_gpio_conifg(RMII_TXD0_GPIO_NUM     , RMII_TXD0_PIN);
    eth_rmii_gpio_conifg(RMII_TXD1_GPIO_NUM     , RMII_TXD1_PIN);
    
    eth_rmii_gpio_conifg(RMII_MDC_GPIO_NUM      , RMII_MDC_PIN);
	eth_rmii_gpio_conifg(RMII_RXD0_GPIO_NUM     , RMII_RXD0_PIN);
    eth_rmii_gpio_conifg(RMII_RXD1_GPIO_NUM     , RMII_RXD1_PIN);  
}


/*����RMII���ã�ͳһ�Ǹ���ģʽ GPIO_AF_11 ��������������������ٶ����*/
static void eth_rmii_gpio_conifg(RMII_GPIO_NUM gpio_periph,RMII_GPIO_PIN pin)
{
    gpio_mode_set(gpio_periph, GPIO_MODE_AF, GPIO_PUPD_NONE, pin);
    gpio_output_options_set(gpio_periph, GPIO_OTYPE_PP, GPIO_OSPEED_MAX, pin);
    gpio_af_set(gpio_periph, GPIO_AF_11, pin);
}

/*�ⲿ50MHzʱ�����ú���*/
static void enet_external_clock_config(void)
{
    /*
     * Ӳ������˵����
     * - �ⲿ50MHzʱ��Դͬʱ���ӵ���
     *   1. LAN8720A��XTAL1(CLKIN)���� - ΪPHY�ṩʱ��
     *   2. GD32F4xx��PA1(RMII_REF_CLK)���� - ΪRMII�ӿ��ṩ�ο�ʱ��
     *
     * ����Ҫ�㣺
     * - PA1����ΪRMII_REF_CLK����ģʽ
     * - ����Ҫ����MCU��CK_OUT0���
     * - ȷ���ⲿʱ���ź��������ã�����/�½�ʱ��<2ns
     */

    printf("Using external 50MHz clock for LAN8720A and RMII interface\r\n");
    printf("External clock configuration completed\r\n");
}

