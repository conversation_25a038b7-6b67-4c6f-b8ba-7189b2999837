#include "enet.h"
#include "gd32f4xx_enet.h"

#define configMAC_ADDR0		2
#define configMAC_ADDR1		0xA
#define configMAC_ADDR2		0xC
#define configMAC_ADDR3		0x5
#define configMAC_ADDR4		0x6
#define configMAC_ADDR5		0x6

extern enet_descriptors_struct	txdesc_tab[ENET_TXBUF_NUM];/*ENET TxDMA描述符, 地址会被存入DMX_RDTADDR寄存器中*/
extern enet_descriptors_struct  rxdesc_tab[ENET_RXBUF_NUM];/*ENET RxDMA描述符，地址会被存入DMX_TDTADDR寄存器中*/

static void eth_rmii_gpio_conifg(RMII_GPIO_NUM gpio_periph,RMII_GPIO_PIN pin);
static void enet_gpio_config(void);


/*设置以太网系统(GPIOs, clocks, MAC, DMA, systick)*/
ErrStatus InitialiseNetwork(void)
{
    ErrStatus flag = ERROR;
    /*配置嵌套矢量中断控制器*/
    nvic_irq_enable(ENET_IRQn, 6 , 0);
    
    enet_gpio_config();                             /* 配置以太网引脚的GPIO端口 */
 
    /* 开启以太网时钟 */
    rcu_periph_clock_enable(RCU_ENET);
    rcu_periph_clock_enable(RCU_ENETTX);
    rcu_periph_clock_enable(RCU_ENETRX);

    enet_deinit();  /* 在AHB总线上重置以太网 */

    enet_software_reset(); //重置位于CLK_TX和CLK_RX的所有核心内部寄存器

    /*用硬件计算和验证IP、UDP、TCP和ICMP的校验和*/
    if (enet_init(ENET_AUTO_NEGOTIATION, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS))
    {
        flag = SUCCESS;
    }
	
    enet_interrupt_enable(ENET_DMA_INT_NIE);        //使能ENET MAC/MSC/DMA中断，正常中断汇总启用
    enet_interrupt_enable(ENET_DMA_INT_RIE);        //接收中断启用
    enet_mac_address_set(ENET_MAC_ADDRESS0,ucMACAddress);//写入mac地址	

    enet_descriptors_chain_init(ENET_DMA_TX);//初始化DMA接收/发送描述符为链模式
    enet_descriptors_chain_init(ENET_DMA_RX); 
    
    for(uint8_t i=0; i<ENET_RXBUF_NUM; i++)//当接收完成时，立即置位ENET_DMA_STAT寄存器的RS位
        enet_rx_desc_immediate_receive_complete_interrupt(&rxdesc_tab[i]);
    
    for(uint8_t i=0; i < ENET_TXBUF_NUM; i++)
        enet_transmit_checksum_config(&txdesc_tab[i], ENET_CHECKSUM_TCPUDPICMP_FULL);
    
    enet_enable();           	 //ENET Tx/Rx功能使能（包括ENET外设内的MAC和DMA模块） 

    return flag;
}
 /*协议栈初始化成功后会调用 vApplicationIPNetworkEventHook() ，所有的IP_task需在里面创建  ethernet_task_creation 放在里面创建*/


/*配置RMII端口*/
static void enet_gpio_config(void)
{
    rcu_periph_clock_enable(RCU_GPIOA);
    rcu_periph_clock_enable(RCU_GPIOB);
    rcu_periph_clock_enable(RCU_GPIOC);
	
	/*CK_OUT0配置，项目没有使用晶振，直接使用MCU输出时钟到PHY,并同步到REF_CLK*/
    /* 选择DIV2在CKOUT0引脚(PA8)上从200MHz获得50MHz到PHY，PLLP：200*/
    
    gpio_af_set(CK_OUT0_GPIO_NUM, GPIO_AF_0, CK_OUT0_PIN);
    gpio_mode_set(CK_OUT0_GPIO_NUM, GPIO_MODE_AF, GPIO_PUPD_NONE, CK_OUT0_PIN);
    gpio_output_options_set(CK_OUT0_GPIO_NUM, GPIO_OTYPE_PP, GPIO_OSPEED_MAX, CK_OUT0_PIN);
    // rcu_ckout0_config(RCU_CKOUT0SRC_PLLP, RCU_CKOUT0_DIV4);//50M
    rcu_ckout0_config(RCU_CKOUT0SRC_HXTAL, RCU_CKOUT0_DIV1);//25M
    
	rcu_periph_clock_enable(RCU_SYSCFG);    							/* 使能SYSCFG时钟 */
	syscfg_enet_phy_interface_config(SYSCFG_ENET_PHY_RMII);//配置以太网MAC的PHY接口
	
	
    eth_rmii_gpio_conifg(RMII_REF_CLK_GPIO_NUM  , RMII_REF_CLK_PIN);
	eth_rmii_gpio_conifg(RMII_MDIO_GPIO_NUM     , RMII_MDIO_PIN);
	eth_rmii_gpio_conifg(RMII_CRS_DV_GPIO_NUM   , RMII_CRS_DV_PIN);
	
	eth_rmii_gpio_conifg(RMII_TX_EN_GPIO_NUM    , RMII_TX_EN_PIN);
	eth_rmii_gpio_conifg(RMII_TXD0_GPIO_NUM     , RMII_TXD0_PIN);
    eth_rmii_gpio_conifg(RMII_TXD1_GPIO_NUM     , RMII_TXD1_PIN);
    
    eth_rmii_gpio_conifg(RMII_MDC_GPIO_NUM      , RMII_MDC_PIN);
	eth_rmii_gpio_conifg(RMII_RXD0_GPIO_NUM     , RMII_RXD0_PIN);
    eth_rmii_gpio_conifg(RMII_RXD1_GPIO_NUM     , RMII_RXD1_PIN);  
}


/*网口RMII配置，统一是复用模式 GPIO_AF_11 ，无上下拉推挽输出，速度最大*/
static void eth_rmii_gpio_conifg(RMII_GPIO_NUM gpio_periph,RMII_GPIO_PIN pin)
{
    gpio_mode_set(gpio_periph, GPIO_MODE_AF, GPIO_PUPD_NONE, pin);
    gpio_output_options_set(gpio_periph, GPIO_OTYPE_PP, GPIO_OSPEED_MAX, pin);
    gpio_af_set(gpio_periph, GPIO_AF_11, pin);
}

