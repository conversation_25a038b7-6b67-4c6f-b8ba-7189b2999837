#include "enet.h"
#include "gd32f4xx_enet.h"

#define configMAC_ADDR0		2
#define configMAC_ADDR1		0xA
#define configMAC_ADDR2		0xC
#define configMAC_ADDR3		0x5
#define configMAC_ADDR4		0x6
#define configMAC_ADDR5		0x6

extern enet_descriptors_struct	txdesc_tab[ENET_TXBUF_NUM];/*ENET TxDMA??????, ?????????DMX_RDTADDR???????*/
extern enet_descriptors_struct  rxdesc_tab[ENET_RXBUF_NUM];/*ENET RxDMA?????????????????DMX_TDTADDR???????*/

static void eth_rmii_gpio_conifg(RMII_GPIO_NUM gpio_periph,RMII_GPIO_PIN pin);
static void enet_gpio_config(void);
static void enet_external_clock_config(void);
static void enet_phy_status_check(void);
static void enet_hardware_check(void);


/*???????????(GPIOs, clocks, MAC, DMA, systick)*/
ErrStatus InitialiseNetwork(void)
{
    ErrStatus flag = ERROR;
    printf("InitialiseNetwork: Starting hardware initialization...\r\n");

    /*???????????????????*/
    nvic_irq_enable(ENET_IRQn, 6 , 0);
    printf("InitialiseNetwork: NVIC configured\r\n");

    enet_gpio_config();                             /* ??????????????GPIO??? */
    printf("InitialiseNetwork: GPIO configured\r\n");
 
    /* ???????????? */
    rcu_periph_clock_enable(RCU_ENET);
    rcu_periph_clock_enable(RCU_ENETTX);
    rcu_periph_clock_enable(RCU_ENETRX);

    enet_deinit();  /* ??AHB??????????????? */
    printf("InitialiseNetwork: ENET deinitialized\r\n");

    enet_software_reset(); //????????CLK_TX??CLK_RX??????????????????
    printf("InitialiseNetwork: ENET software reset completed\r\n");

    /*?????????????IP??UDP??TCP??ICMP???????*/
    printf("InitialiseNetwork: Starting ENET initialization...\r\n");
    printf("InitialiseNetwork: Attempting with auto-negotiation...\r\n");
    if (enet_init(ENET_AUTO_NEGOTIATION, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS))
    {
        printf("InitialiseNetwork: ENET init successful with auto-negotiation\r\n");
        flag = SUCCESS;
    }
    else
    {
        printf("InitialiseNetwork: Auto-negotiation failed, trying fixed 100M Full Duplex...\r\n");
        /* Try with fixed 100M Full Duplex mode */
        if (enet_init(ENET_100M_FULLDUPLEX, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS))
        {
            printf("InitialiseNetwork: ENET init successful with fixed mode\r\n");
            flag = SUCCESS;
        }
        else
        {
            printf("InitialiseNetwork: Both auto and fixed mode failed\r\n");
            printf("InitialiseNetwork: This indicates physical layer issues\r\n");
        }
    }

    /* ????????? */
    printf("InitialiseNetwork: Starting hardware check...\r\n");
    enet_hardware_check();

    /* PHY????? */
    printf("InitialiseNetwork: Starting PHY status check...\r\n");
    enet_phy_status_check();

    enet_interrupt_enable(ENET_DMA_INT_NIE);        //???ENET MAC/MSC/DMA????????????????????
    enet_interrupt_enable(ENET_DMA_INT_RIE);        //????????????
    enet_mac_address_set(ENET_MAC_ADDRESS0,ucMACAddress);//????mac???	

    enet_descriptors_chain_init(ENET_DMA_TX);//?????DMA????/???????????????
    enet_descriptors_chain_init(ENET_DMA_RX); 
    
    for(uint8_t i=0; i<ENET_RXBUF_NUM; i++)//????????????????????ENET_DMA_STAT???????RS??
        enet_rx_desc_immediate_receive_complete_interrupt(&rxdesc_tab[i]);
    
    for(uint8_t i=0; i < ENET_TXBUF_NUM; i++)
        enet_transmit_checksum_config(&txdesc_tab[i], ENET_CHECKSUM_TCPUDPICMP_FULL);
    
    enet_enable();           	 //ENET Tx/Rx????????????ENET???????MAC??DMA??? 

    return flag;
}
 /*??????????????????? vApplicationIPNetworkEventHook() ????????IP_task???????????  ethernet_task_creation ???????????*/


/*????RMII???*/
static void enet_gpio_config(void)
{
    rcu_periph_clock_enable(RCU_GPIOA);
    rcu_periph_clock_enable(RCU_GPIOB);
    rcu_periph_clock_enable(RCU_GPIOC);
	
	/*??50MHz??????? - ?????MCU???????PHY*/
    /* ?????????????LAN8720A??XTAL1(CLKIN)??RMII_REF_CLK */
    /* ????MCU???????????????????50MHz???? */

    // gpio_af_set(CK_OUT0_GPIO_NUM, GPIO_AF_0, CK_OUT0_PIN);
    // gpio_mode_set(CK_OUT0_GPIO_NUM, GPIO_MODE_AF, GPIO_PUPD_NONE, CK_OUT0_PIN);
    // gpio_output_options_set(CK_OUT0_GPIO_NUM, GPIO_OTYPE_PP, GPIO_OSPEED_MAX, CK_OUT0_PIN);
    // rcu_ckout0_config(RCU_CKOUT0SRC_PLLP, RCU_CKOUT0_DIV4);//50M - ?????
    // rcu_ckout0_config(RCU_CKOUT0SRC_HXTAL, RCU_CKOUT0_DIV1);//25M - ?????
    
	rcu_periph_clock_enable(RCU_SYSCFG);    							/* ???SYSCFG??? */
	syscfg_enet_phy_interface_config(SYSCFG_ENET_PHY_RMII);//?????????MAC??PHY???

	/* ??????50MHz??? */
	enet_external_clock_config();

    /* RMII_REF_CLK (PA1) ???????????????????50MHz??? */
    gpio_mode_set(RMII_REF_CLK_GPIO_NUM, GPIO_MODE_AF, GPIO_PUPD_NONE, RMII_REF_CLK_PIN);
    gpio_af_set(RMII_REF_CLK_GPIO_NUM, GPIO_AF_11, RMII_REF_CLK_PIN);
	eth_rmii_gpio_conifg(RMII_MDIO_GPIO_NUM     , RMII_MDIO_PIN);
	eth_rmii_gpio_conifg(RMII_CRS_DV_GPIO_NUM   , RMII_CRS_DV_PIN);
	
	eth_rmii_gpio_conifg(RMII_TX_EN_GPIO_NUM    , RMII_TX_EN_PIN);
	eth_rmii_gpio_conifg(RMII_TXD0_GPIO_NUM     , RMII_TXD0_PIN);
    eth_rmii_gpio_conifg(RMII_TXD1_GPIO_NUM     , RMII_TXD1_PIN);
    
    eth_rmii_gpio_conifg(RMII_MDC_GPIO_NUM      , RMII_MDC_PIN);
	eth_rmii_gpio_conifg(RMII_RXD0_GPIO_NUM     , RMII_RXD0_PIN);
    eth_rmii_gpio_conifg(RMII_RXD1_GPIO_NUM     , RMII_RXD1_PIN);  
}


/*????RMII?????????????? GPIO_AF_11 ?????????????????????????*/
static void eth_rmii_gpio_conifg(RMII_GPIO_NUM gpio_periph,RMII_GPIO_PIN pin)
{
    gpio_mode_set(gpio_periph, GPIO_MODE_AF, GPIO_PUPD_NONE, pin);
    gpio_output_options_set(gpio_periph, GPIO_OTYPE_PP, GPIO_OSPEED_MAX, pin);
    gpio_af_set(gpio_periph, GPIO_AF_11, pin);
}

/*??50MHz???????????*/
static void enet_external_clock_config(void)
{
    /*
     * ????????????
     * - ??50MHz?????????????
     *   1. LAN8720A??XTAL1(CLKIN)???? - ?PHY?????
     *   2. GD32F4xx??PA1(RMII_REF_CLK)???? - ?RMII????????????
     *
     * ???????
     * - PA1?????RMII_REF_CLK??????
     * - ?????????MCU??CK_OUT0???
     * - ????????????????????????/??????<2ns
     */

    printf("Using external 50MHz clock for LAN8720A and RMII interface\r\n");
    printf("External clock configuration completed\r\n");
}

/*PHY???????*/
static void enet_phy_status_check(void)
{
    uint16_t phy_value = 0;
    ErrStatus status;

    printf("=== PHY Status Check ===\r\n");

    /* ???MDIO??????? */
    printf("ENET_MAC_PHY_CTL: 0x%08X\r\n", ENET_MAC_PHY_CTL);

    /* ????????PHY??? */
    for(uint16_t addr = 0; addr < 4; addr++) {
        printf("Trying PHY address %d...\r\n", addr);

        /* ???PHY ID?????1 */
        status = enet_phy_write_read(ENET_PHY_READ, addr, 2, &phy_value);
        if(status == SUCCESS && phy_value != 0xFFFF && phy_value != 0x0000) {
            printf("PHY found at address %d, ID1: 0x%04X\r\n", addr, phy_value);

            /* ???PHY ID?????2 */
            status = enet_phy_write_read(ENET_PHY_READ, addr, 3, &phy_value);
            if(status == SUCCESS) {
                printf("PHY ID2: 0x%04X\r\n", phy_value);

                /* LAN8720A??ID?????: ID1=0x0007, ID2=0xC0F1 */
                if(phy_value == 0xC0F1) {
                    printf("LAN8720A detected at address %d!\r\n", addr);
                    printf("NOTE: PHY_ADDRESS is now set to %d\r\n", PHY_ADDRESS);
                }
            }
            break;
        }
    }

    /* ??????????????? */
    printf("Using default PHY address %d for detailed check...\r\n", PHY_ADDRESS);

    /* ??????????????? */
    status = enet_phy_write_read(ENET_PHY_READ, PHY_ADDRESS, PHY_REG_BCR, &phy_value);
    if(status == SUCCESS) {
        printf("PHY BCR: 0x%04X\r\n", phy_value);
    } else {
        printf("Failed to read PHY BCR\r\n");
    }

    /* ?????????????? */
    status = enet_phy_write_read(ENET_PHY_READ, PHY_ADDRESS, PHY_REG_BSR, &phy_value);
    if(status == SUCCESS) {
        printf("PHY BSR: 0x%04X\r\n", phy_value);
        if(phy_value & PHY_LINKED_STATUS) {
            printf("PHY Link: UP\r\n");
        } else {
            printf("PHY Link: DOWN - Waiting for connection...\r\n");

            /* Wait for link establishment */
            uint32_t link_timeout = 0;
            while(link_timeout < 30) {  // Wait up to 3 seconds
                vTaskDelay(100);  // Wait 100ms
                status = enet_phy_write_read(ENET_PHY_READ, PHY_ADDRESS, PHY_REG_BSR, &phy_value);
                if(status == SUCCESS && (phy_value & PHY_LINKED_STATUS)) {
                    printf("PHY Link: UP (established after %d00ms)\r\n", link_timeout + 1);
                    break;
                }
                link_timeout++;
                if(link_timeout % 10 == 0) {
                    printf("Still waiting for link... (%ds)\r\n", link_timeout/10);
                }
            }

            if(link_timeout >= 30) {
                printf("PHY Link: Still DOWN after 3s timeout\r\n");
                printf("Check: 1) Network cable  2) Switch/Router  3) Cable integrity\r\n");

                /* Read additional PHY registers for diagnosis */
                printf("=== Extended PHY Diagnosis ===\r\n");

                /* Read PHY Control Register 1 (if available) */
                status = enet_phy_write_read(ENET_PHY_READ, PHY_ADDRESS, 17, &phy_value);
                if(status == SUCCESS) {
                    printf("PHY Reg 17 (Control): 0x%04X\r\n", phy_value);
                }

                /* Read PHY Status Register (31) */
                status = enet_phy_write_read(ENET_PHY_READ, PHY_ADDRESS, 31, &phy_value);
                if(status == SUCCESS) {
                    printf("PHY Reg 31 (Status): 0x%04X\r\n", phy_value);
                    if(phy_value & 0x0004) {
                        printf("Speed: 10Mbps\r\n");
                    } else {
                        printf("Speed: 100Mbps\r\n");
                    }
                    if(phy_value & 0x0010) {
                        printf("Duplex: Full\r\n");
                    } else {
                        printf("Duplex: Half\r\n");
                    }
                }

                /* Try to force 100M Full Duplex */
                printf("Trying to force 100M Full Duplex...\r\n");
                phy_value = 0x2100;  // 100M, Full Duplex, Auto-neg disabled
                status = enet_phy_write_read(ENET_PHY_WRITE, PHY_ADDRESS, PHY_REG_BCR, &phy_value);
                if(status == SUCCESS) {
                    printf("Forced mode set, waiting 2s...\r\n");
                    vTaskDelay(2000);

                    /* Check link again */
                    status = enet_phy_write_read(ENET_PHY_READ, PHY_ADDRESS, PHY_REG_BSR, &phy_value);
                    if(status == SUCCESS && (phy_value & PHY_LINKED_STATUS)) {
                        printf("Link established in forced mode!\r\n");
                    } else {
                        printf("Link still down in forced mode\r\n");

                        /* Restore auto-negotiation */
                        phy_value = 0x3000;  // Auto-negotiation enabled
                        enet_phy_write_read(ENET_PHY_WRITE, PHY_ADDRESS, PHY_REG_BCR, &phy_value);
                    }
                }
                printf("=== End Extended Diagnosis ===\r\n");
            }
        }

        if(phy_value & PHY_AUTONEGO_COMPLETE) {
            printf("Auto-negotiation: COMPLETE\r\n");
        } else {
            printf("Auto-negotiation: IN PROGRESS\r\n");
        }
    } else {
        printf("Failed to read PHY BSR\r\n");
    }

    printf("=== End PHY Status ===\r\n");
}

/*????????�D??*/
static void enet_hardware_check(void)
{
    printf("=== Hardware Connection Check ===\r\n");

    /* ???GPIO???? */
    printf("GPIOA_CTL: 0x%08X\r\n", GPIO_CTL(GPIOA));
    printf("GPIOA_OMODE: 0x%08X\r\n", GPIO_OMODE(GPIOA));
    printf("GPIOA_OSPD: 0x%08X\r\n", GPIO_OSPD(GPIOA));
    printf("GPIOA_PUD: 0x%08X\r\n", GPIO_PUD(GPIOA));
    printf("GPIOA_AFSEL0: 0x%08X\r\n", GPIO_AFSEL0(GPIOA));

    printf("GPIOB_CTL: 0x%08X\r\n", GPIO_CTL(GPIOB));
    printf("GPIOB_AFSEL1: 0x%08X\r\n", GPIO_AFSEL1(GPIOB));

    printf("GPIOC_CTL: 0x%08X\r\n", GPIO_CTL(GPIOC));
    printf("GPIOC_AFSEL0: 0x%08X\r\n", GPIO_AFSEL0(GPIOC));

    /* ?????????? */
    printf("RCU_CFG0: 0x%08X\r\n", RCU_CFG0);
    printf("RCU_AHB1EN: 0x%08X\r\n", RCU_AHB1EN);
    printf("SYSCFG_CFG1: 0x%08X\r\n", SYSCFG_CFG1);

    /* ??????????? */
    if(RCU_AHB1EN & RCU_AHB1EN_ENETEN) {
        printf("ENET clock: ENABLED\r\n");
    } else {
        printf("ENET clock: DISABLED\r\n");
    }

    if(RCU_AHB1EN & RCU_AHB1EN_ENETTXEN) {
        printf("ENET TX clock: ENABLED\r\n");
    } else {
        printf("ENET TX clock: DISABLED\r\n");
    }

    if(RCU_AHB1EN & RCU_AHB1EN_ENETRXEN) {
        printf("ENET RX clock: ENABLED\r\n");
    } else {
        printf("ENET RX clock: DISABLED\r\n");
    }

    /* ???RMII???? */
    if(SYSCFG_CFG1 & SYSCFG_CFG1_ENET_PHY_SEL) {
        printf("PHY interface: RMII\r\n");
    } else {
        printf("PHY interface: MII\r\n");
    }

    printf("=== End Hardware Check ===\r\n");
}

