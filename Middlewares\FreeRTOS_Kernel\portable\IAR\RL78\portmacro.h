/*
 * FreeRTOS Kernel V10.5.1
 * Copyright (C) 2021 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
 *
 * SPDX-License-Identifier: MIT
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 * https://www.FreeRTOS.org
 * https://github.com/FreeRTOS
 *
 */

#ifndef PORTMACRO_H
#define PORTMACRO_H

#ifdef __IAR_SYSTEMS_ICC__

#ifdef __cplusplus
extern "C" {
#endif

/*-----------------------------------------------------------
 * Port specific definitions.
 *
 * The settings in this file configure FreeRTOS correctly for the
 * given hardware and compiler.
 *
 * These settings should not be altered.
 *-----------------------------------------------------------
 */

#if __DATA_MODEL__ == __DATA_MODEL_FAR__ && __CODE_MODEL__ == __CODE_MODEL_NEAR__
    #warning This port has not been tested with your selected memory model combination. If a far data model is required it is recommended to also use a far code model.
#endif

#if __DATA_MODEL__ == __DATA_MODEL_NEAR__ && __CODE_MODEL__ == __CODE_MODEL_FAR__
    #warning This port has not been tested with your selected memory model combination. If a far code model is required it is recommended to also use a far data model.
#endif

/* Type definitions. */

#define portCHAR        char
#define portFLOAT       float
#define portDOUBLE      double
#define portLONG        long
#define portSHORT       short
#define portSTACK_TYPE  uint16_t
#define portBASE_TYPE   short

typedef portSTACK_TYPE StackType_t;
typedef short BaseType_t;
typedef unsigned short UBaseType_t;


#if __DATA_MODEL__ == __DATA_MODEL_FAR__
    #define portPOINTER_SIZE_TYPE uint32_t
#else
    #define portPOINTER_SIZE_TYPE uint16_t
#endif


#if ( configUSE_16_BIT_TICKS == 1 )
    typedef unsigned int TickType_t;
    #define portMAX_DELAY ( TickType_t ) 0xffff
#else
    typedef uint32_t TickType_t;
    #define portMAX_DELAY ( TickType_t ) 0xffffffffUL
#endif
/*-----------------------------------------------------------*/

/* Interrupt control macros. */
#define portDISABLE_INTERRUPTS()    __asm ( "DI" )
#define portENABLE_INTERRUPTS()     __asm ( "EI" )
/*-----------------------------------------------------------*/

/* Critical section control macros. */
#define portNO_CRITICAL_SECTION_NESTING        ( ( uint16_t ) 0 )

#define portENTER_CRITICAL()                                                   \
{                                                                              \
extern volatile uint16_t usCriticalNesting;                                    \
                                                                               \
    portDISABLE_INTERRUPTS();                                                  \
                                                                               \
    /* Now interrupts are disabled ulCriticalNesting can be accessed */        \
    /* directly.  Increment ulCriticalNesting to keep a count of how many */   \
    /* times portENTER_CRITICAL() has been called. */                          \
    usCriticalNesting++;                                                       \
}

#define portEXIT_CRITICAL()                                                    \
{                                                                              \
extern volatile uint16_t usCriticalNesting;                                    \
                                                                               \
    if( usCriticalNesting > portNO_CRITICAL_SECTION_NESTING )                  \
    {                                                                          \
        /* Decrement the nesting count when leaving a critical section. */     \
        usCriticalNesting--;                                                   \
                                                                               \
        /* If the nesting level has reached zero then interrupts should be */  \
        /* re-enabled. */                                                      \
        if( usCriticalNesting == portNO_CRITICAL_SECTION_NESTING )             \
        {                                                                      \
            portENABLE_INTERRUPTS();                                           \
        }                                                                      \
    }                                                                          \
}
/*-----------------------------------------------------------*/

/* Task utilities. */
#define portNOP()      __asm( "NOP" )
#define portYIELD()    __asm( "BRK" )
#define portYIELD_FROM_ISR( xHigherPriorityTaskWoken ) do { if( xHigherPriorityTaskWoken ) vTaskSwitchContext(); } while( 0 )
/*-----------------------------------------------------------*/

/* Hardwware specifics. */
#define portBYTE_ALIGNMENT   2
#define portSTACK_GROWTH     ( -1 )
#define portTICK_PERIOD_MS   ( ( TickType_t ) 1000 / configTICK_RATE_HZ )
/*-----------------------------------------------------------*/

/* Task function macros as described on the FreeRTOS.org WEB site. */
#define portTASK_FUNCTION_PROTO( vFunction, pvParameters ) void vFunction( void *pvParameters )
#define portTASK_FUNCTION( vFunction, pvParameters ) void vFunction( void *pvParameters )

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __IAR_SYSTEMS_ICC__ */

;//-----------------------------------------------------------------------------
;// The macros below are processed for asm sources which include portmacro.h.
;//-----------------------------------------------------------------------------
#ifdef __IAR_SYSTEMS_ASM__

;/* Functions and variables used by this file. */
;//-----------------------------------------------------------------------------
    EXTERN _pxCurrentTCB
    EXTERN _usCriticalNesting

;/* Macro used to declutter calls, depends on the selected code model. */
;//-----------------------------------------------------------------------------
#if __CODE_MODEL__ == __CODE_MODEL_FAR__
    #define RCALL(X)    CALL    F:X
#else
    #define RCALL(X)    CALL    X
#endif


;/*-----------------------------------------------------------------------------
; * portSAVE_CONTEXT MACRO
; * Saves the context of the general purpose registers, CS and ES (only in __far
; * memory mode) registers the _usCriticalNesting value and the Stack Pointer
; * of the active Task onto the task stack.
; *---------------------------------------------------------------------------*/
portSAVE_CONTEXT MACRO
    PUSH      AX                       ; // Save AX Register to stack.
    PUSH      HL
#if  __CODE_MODEL__  == __CODE_MODEL_FAR__
    MOV       A, CS                    ; // Save CS register.
    XCH       A, X
    MOV       A, ES                    ; // Save ES register.
    PUSH      AX
#else
    MOV       A, CS                    ; // Save CS register.
    PUSH      AX
#endif
    PUSH      DE                       ; // Save the remaining general purpose registers.
    PUSH      BC
    MOVW      AX, _usCriticalNesting   ; // Save the _usCriticalNesting value.
    PUSH      AX
    MOVW      AX, _pxCurrentTCB        ; // Save the Task stack pointer.
    MOVW      HL, AX
    MOVW      AX, SP
    MOVW      [HL], AX
    ENDM
;//-----------------------------------------------------------------------------


;/*-----------------------------------------------------------------------------
; * portRESTORE_CONTEXT MACRO
; * Restores the task Stack Pointer then use this to restore _usCriticalNesting,
; * general purpose registers and the CS and ES (only in __far memory mode)
; * of the selected task from the task stack.
; *---------------------------------------------------------------------------*/
portRESTORE_CONTEXT MACRO
    MOVW    AX, _pxCurrentTCB          ; // Restore the Task stack pointer.
    MOVW    HL, AX
    MOVW    AX, [HL]
    MOVW    SP, AX
    POP     AX                         ; // Restore _usCriticalNesting value.
    MOVW    _usCriticalNesting, AX
    POP     BC                         ; // Restore the necessary general purpose registers.
    POP     DE
#if __CODE_MODEL__  == __CODE_MODEL_FAR__
    POP     AX                         ; // Restore the ES register.
    MOV     ES, A
    XCH     A, X                       ; // Restore the CS register.
    MOV     CS, A
#else
    POP     AX
    MOV     CS, A                      ; // Restore CS register.
#endif
    POP     HL                         ; // Restore general purpose register HL.
    POP     AX                         ; // Restore AX.
    ENDM
;//-----------------------------------------------------------------------------

#endif /* __IAR_SYSTEMS_ASM__ */

#endif /* PORTMACRO_H */
