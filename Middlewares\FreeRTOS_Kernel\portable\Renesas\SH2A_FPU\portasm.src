;/*
; * FreeRTOS Kernel V10.5.1
; * Copyright (C) 2021 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
; *
; * SPDX-License-Identifier: MIT
; *
; * Permission is hereby granted, free of charge, to any person obtaining a copy of
; * this software and associated documentation files (the "Software"), to deal in
; * the Software without restriction, including without limitation the rights to
; * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
; * the Software, and to permit persons to whom the Software is furnished to do so,
; * subject to the following conditions:
; *
; * The above copyright notice and this permission notice shall be included in all
; * copies or substantial portions of the Software.
; *
; * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
; * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
; * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
; * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WH<PERSON>H<PERSON>
; * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
; * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
; *
; * https://www.FreeRTOS.org
; * https://github.com/FreeRTOS
; *
; */

	.import _pxCurrentTCB
	.import _vTaskSwitchContext
	.import _xTaskIncrementTick

	.export _vPortStartFirstTask
	.export _ulPortGetGBR
	.export _vPortYieldHandler
	.export _vPortPreemptiveTick
	.export _vPortCooperativeTick
	.export _vPortSaveFlopRegisters
	.export _vPortRestoreFlopRegisters

    .section    P

	.INCLUDE "ISR_Support.inc"

_vPortStartFirstTask:

	portRESTORE_CONTEXT

;-----------------------------------------------------------

_vPortYieldHandler:

	portSAVE_CONTEXT

	mov.l	#_vTaskSwitchContext, r0
	jsr		@r0
	nop

	portRESTORE_CONTEXT

;-----------------------------------------------------------

_vPortPreemptiveTick

	portSAVE_CONTEXT

	mov.l	#_xTaskIncrementTick, r0
	jsr		@r0
	nop

	mov.l	#_vTaskSwitchContext, r0
	jsr		@r0
	nop

	portRESTORE_CONTEXT

;-----------------------------------------------------------

_vPortCooperativeTick

	portSAVE_CONTEXT

	mov.l	#_xTaskIncrementTick, r0
	jsr		@r0
	nop

	portRESTORE_CONTEXT

;-----------------------------------------------------------

_ulPortGetGBR:

	stc.l	gbr, r0
	rts
	nop

;-----------------------------------------------------------

_vPortSaveFlopRegisters:

	fmov.s	fr0, @-r4
	fmov.s	fr1, @-r4
	fmov.s	fr2, @-r4
	fmov.s	fr3, @-r4
	fmov.s	fr4, @-r4
	fmov.s	fr5, @-r4
	fmov.s	fr6, @-r4
	fmov.s	fr7, @-r4
	fmov.s	fr8, @-r4
	fmov.s	fr9, @-r4
	fmov.s	fr10, @-r4
	fmov.s	fr11, @-r4
	fmov.s	fr12, @-r4
	fmov.s	fr13, @-r4
	fmov.s	fr14, @-r4
	fmov.s	fr15, @-r4
	sts.l   fpul, @-r4
	sts.l   fpscr, @-r4

	rts
	nop

;-----------------------------------------------------------

_vPortRestoreFlopRegisters:

	add.l  #-72, r4
	lds.l  @r4+, fpscr
	lds.l  @r4+, fpul
	fmov.s @r4+, fr15
	fmov.s @r4+, fr14
	fmov.s @r4+, fr13
	fmov.s @r4+, fr12
	fmov.s @r4+, fr11
	fmov.s @r4+, fr10
	fmov.s @r4+, fr9
	fmov.s @r4+, fr8
	fmov.s @r4+, fr7
	fmov.s @r4+, fr6
	fmov.s @r4+, fr5
	fmov.s @r4+, fr4
	fmov.s @r4+, fr3
	fmov.s @r4+, fr2
	fmov.s @r4+, fr1
	fmov.s @r4+, fr0

	rts
	nop

	.end

