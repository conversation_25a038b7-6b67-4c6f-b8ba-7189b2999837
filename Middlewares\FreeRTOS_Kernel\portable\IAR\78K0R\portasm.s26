;/*
; * FreeRTOS Kernel V10.5.1
; * Copyright (C) 2021 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
; *
; * SPDX-License-Identifier: MIT
; *
; * Permission is hereby granted, free of charge, to any person obtaining a copy of
; * this software and associated documentation files (the "Software"), to deal in
; * the Software without restriction, including without limitation the rights to
; * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
; * the Software, and to permit persons to whom the Software is furnished to do so,
; * subject to the following conditions:
; *
; * The above copyright notice and this permission notice shall be included in all
; * copies or substantial portions of the Software.
; *
; * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
; * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
; * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
; * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WH<PERSON>H<PERSON>
; * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
; * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
; *
; * https://www.FreeRTOS.org
; * https://github.com/FreeRTOS
; *
; */

#include "ISR_Support.h"
;------------------------------------------------------------------------------

#if __CORE__ != __78K0R__
	#error "This file is only for 78K0R Devices"
#endif

#define CS                    0xFFFFC
#define ES                    0xFFFFD

; Functions implemented in this file
;------------------------------------------------------------------------------
	PUBLIC    vPortYield
	PUBLIC    vPortStart

; Functions used by scheduler
;------------------------------------------------------------------------------
	EXTERN    vTaskSwitchContext
	EXTERN    xTaskIncrementTick

; Tick ISR Prototype
;------------------------------------------------------------------------------
;	EXTERN    ?CL78K0R_V2_L00

	PUBWEAK   `??MD_INTTM05??INTVEC 68`
	PUBLIC    MD_INTTM05

MD_INTTM05    SYMBOL "MD_INTTM05"
`??MD_INTTM05??INTVEC 68` SYMBOL "??INTVEC 68", MD_INTTM05



;------------------------------------------------------------------------------
;   Yield to another task.  Implemented as a software interrupt.  The return
;   address and PSW will have been saved to the stack automatically before
;   this code runs.
;
;   Input:  NONE
;
;   Call:   CALL    vPortYield
;
;   Output: NONE
;
;------------------------------------------------------------------------------
    RSEG CODE:CODE
vPortYield:
	portSAVE_CONTEXT		        ; Save the context of the current task.
	call      vTaskSwitchContext    ; Call the scheduler to select the next task.
	portRESTORE_CONTEXT		        ; Restore the context of the next task to run.
	retb


;------------------------------------------------------------------------------
;   Restore the context of the first task that is going to run.
;
;   Input:  NONE
;
;   Call:   CALL    vPortStart
;
;   Output: NONE
;
;------------------------------------------------------------------------------
    RSEG CODE:CODE
vPortStart:
	portRESTORE_CONTEXT	            ; Restore the context of whichever task the ...
	reti					        ; An interrupt stack frame is used so the task
                                    ; is started using a RETI instruction.

;------------------------------------------------------------------------------
;   Perform the necessary steps of the Tick Count Increment and Task Switch
;   depending on the chosen kernel configuration
;
;   Input:  NONE
;
;   Call:   ISR
;
;   Output: NONE
;
;------------------------------------------------------------------------------

MD_INTTM05:

	portSAVE_CONTEXT		        ; Save the context of the current task.
	call      xTaskIncrementTick    ; Call the timer tick function.
#if configUSE_PREEMPTION == 1
	call      vTaskSwitchContext    ; Call the scheduler to select the next task.
#endif
	portRESTORE_CONTEXT		        ; Restore the context of the next task to run.
	reti



;	REQUIRE ?CL78K0R_V2_L00
	COMMON INTVEC:CODE:ROOT(1)      ; Set ISR location to the Interrupt vector table.
	ORG 68
`??MD_INTTM05??INTVEC 68`:
	DW MD_INTTM05

	COMMON INTVEC:CODE:ROOT(1)      ; Set ISR location to the Interrupt vector table.
	ORG 126
`??vPortYield??INTVEC 126`:
	DW vPortYield

									; Set value for the usCriticalNesting.
	RSEG NEAR_ID:CONST:SORT:NOROOT(1)
`?<Initializer for usCriticalNesting>`:
	DW 10

;#endif

      END
