#include "uart0.h"


void uart0_send_data(uint8_t *data,uint8_t len)
{
    for(uint8_t i=0;i<len;i++)
    {   
        while(usart_flag_get(USART0,USART_FLAG_TBE)!=SET);
        usart_data_transmit(USART0,data[i]);
        while(usart_flag_get(USART0,USART_FLAG_TC)!=SET);
    }
}



/*串口0配置函数，因为串口0只做调试用，所以不写入global_config.h中，直接粘贴官方的例程*/
void uart0_init(uint32_t baudval)
{	
    rcu_periph_clock_enable(RCU_GPIOA);    /*使能GPIO时钟*/
    rcu_periph_clock_enable(RCU_USART0);/*使能USART时钟 */
    gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_9);/*配置USART0 TX引脚和USART0 RX引脚*/
    gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_10);

    /*配置USART0 TX上拉推挽 */
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_9);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_9);

    /*配置USART0 RX作为备用功能推拉*/
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_10);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_10);

    /* USART配置*/
    usart_deinit(USART0);
    usart_baudrate_set(USART0, baudval);
    //usart_receive_config(USART0, USART_RECEIVE_ENABLE);
    usart_transmit_config(USART0, USART_TRANSMIT_ENABLE);
    usart_enable(USART0);

}


/* 重写printf函数*/

#pragma import(__use_no_semihosting)
//标准库需要的支持函数
struct __FILE
{
    int handle;
};
FILE __stdout;

void _sys_exit(int x)
{
    x = x;
}

void _ttywrch(int ch)
{
    ch = ch;
}
 
int fputc(int ch, FILE *f)
{
    usart_data_transmit(USART0, (uint8_t)ch);
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    return ch;
}
