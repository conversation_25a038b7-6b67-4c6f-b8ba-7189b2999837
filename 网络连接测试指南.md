# 网络连接测试指南

## 🎉 当前进展
✅ **PHY通信正常** - 寄存器读写成功  
✅ **LAN8720A检测成功** - 芯片ID正确  
✅ **PHY地址配置正确** - 地址1  
⏳ **等待物理链路建立** - 需要网线连接  

## 🔌 网络连接检查清单

### 1. 物理连接检查
- [ ] **网线连接** - 确保网线两端连接牢固
- [ ] **网线质量** - 使用已知良好的网线
- [ ] **RJ45接口** - 检查接口是否损坏
- [ ] **网络设备** - 确保交换机/路由器正常工作

### 2. LED指示检查
LAN8720A芯片通常有LED指示：
- **LED1 (引脚13)** - 链路状态指示
  - 亮：链路建立
  - 灭：无链路
- **LED2 (引脚14)** - 活动状态指示
  - 闪烁：有数据传输
  - 灭：无数据传输

### 3. 测试步骤

#### 步骤1: 编译并下载新代码
新代码包含链路等待功能，会自动等待网络连接。

#### 步骤2: 连接网线
将网线一端连接到您的开发板，另一端连接到：
- 路由器/交换机的任意端口
- 或直接连接到电脑网卡（需要交叉线或支持自动翻转的设备）

#### 步骤3: 观察串口输出
正常情况下应该看到：
```
PHY Link: DOWN - Waiting for connection...
PHY Link: UP (established after 200ms)
Auto-negotiation: COMPLETE
enet_init ok
IP Address: ************
Gateway IP: ***********
Subnet Mask: *************
```

#### 步骤4: 网络连通性测试
如果网络初始化成功，可以进行：
- **Ping测试** - 从电脑ping开发板IP
- **TCP连接测试** - 如果有TCP服务器代码

## 🔧 故障排除

### 问题1: 链路始终无法建立
**可能原因：**
- 网线问题（断线、接触不良）
- 网络设备故障
- RJ45接口问题
- 网络变压器问题

**解决方法：**
1. 更换网线
2. 连接到不同的网络端口
3. 检查LED指示灯
4. 用万用表检查RJ45引脚连通性

### 问题2: 链路建立但网络不通
**可能原因：**
- IP地址冲突
- 子网配置错误
- 网关设置错误

**解决方法：**
1. 修改IP地址配置
2. 检查网络参数设置
3. 确认与路由器在同一网段

### 问题3: 自动协商失败
**可能原因：**
- 网络设备不支持自动协商
- PHY配置问题

**解决方法：**
1. 尝试固定速度和双工模式
2. 检查网络设备设置

## 📊 当前网络配置

根据 `user/main.h` 中的设置：
- **IP地址**: ************
- **子网掩码**: *************
- **网关**: ***********
- **DNS**: **************

确保这些设置与您的网络环境匹配。

## 🎯 预期结果

成功连接后，您应该能够：
1. **看到链路建立消息**
2. **获得IP地址配置信息**
3. **从电脑ping通开发板**
4. **进行TCP/UDP通信**

## 📞 下一步

请：
1. 编译并下载新代码
2. 连接网线
3. 观察串口输出
4. 报告结果

如果链路仍无法建立，请检查硬件连接和网线质量。
