.\objects\bufferallocation_2.o: Middlewares\FreeRTOS_TCP\portable\BufferManagement\BufferAllocation_2.c
.\objects\bufferallocation_2.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h
.\objects\bufferallocation_2.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\bufferallocation_2.o: .\user\FreeRTOSConfig.h
.\objects\bufferallocation_2.o: .\CMSIS\gd32f4xx.h
.\objects\bufferallocation_2.o: .\CMSIS\core_cm4.h
.\objects\bufferallocation_2.o: .\CMSIS\core_cmInstr.h
.\objects\bufferallocation_2.o: .\CMSIS\core_cmFunc.h
.\objects\bufferallocation_2.o: .\CMSIS\core_cm4_simd.h
.\objects\bufferallocation_2.o: .\CMSIS\system_gd32f4xx.h
.\objects\bufferallocation_2.o: .\CMSIS\gd32f4xx_libopt.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_rcu.h
.\objects\bufferallocation_2.o: .\CMSIS\gd32f4xx.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_adc.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_can.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_crc.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_ctc.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_dac.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_dbg.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_dci.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_dma.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_exti.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_fmc.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_fwdgt.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_gpio.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_syscfg.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_i2c.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_iref.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_pmu.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_rtc.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_sdio.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_spi.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_timer.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_trng.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_usart.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_wwdgt.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_misc.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_enet.h
.\objects\bufferallocation_2.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_exmc.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_ipa.h
.\objects\bufferallocation_2.o: .\FWLib\Include\gd32f4xx_tli.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\include\projdefs.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\include\portable.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\include\task.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\include\list.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\include\semphr.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\include\queue.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h
.\objects\bufferallocation_2.o: .\user\FreeRTOSIPConfig.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h
.\objects\bufferallocation_2.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\bufferallocation_2.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\include\event_groups.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_Kernel\include\timers.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h
.\objects\bufferallocation_2.o: .\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h
