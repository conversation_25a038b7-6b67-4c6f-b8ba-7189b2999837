#ifndef _MAIN_H_
#define _MAIN_H_

#include "gd32f4xx.h"
#include <stdio.h>

#include "FreeRTOS.h"
#include "task.h"

#include "FreeRTOS_IP.h"

#include "uart0.h"

int fputc(int ch, FILE *f);

/* Ĭ��MAC��ַ���á�*/
#define configMAC_ADDR0		2
#define configMAC_ADDR1		0xA
#define configMAC_ADDR2		0xC
#define configMAC_ADDR3		0x5
#define configMAC_ADDR4		0x6
#define configMAC_ADDR5		0x6
 
/* Ĭ��IP��ַ���� - �޸�Ϊ192.168.6.x���� */
#define configIP_ADDR0		192
#define configIP_ADDR1		168
#define configIP_ADDR2		31
#define configIP_ADDR3		11


/* Ĭ������IP��ַ���� - �޸�Ϊ192.168.6.x���� */
#define configGATEWAY_ADDR0	192
#define configGATEWAY_ADDR1	168
#define configGATEWAY_ADDR2	31
#define configGATEWAY_ADDR3	1

/* Ĭ�ϵ������������á�*/
#define configNET_MASK0		255
#define configNET_MASK1		255
#define configNET_MASK2		255
#define configNET_MASK3		0

/* Ĭ��DNS���������á�
OpenDNS��ַΪ**************��**************��*/

#define configDNS_SERVER_ADDR0 	208
#define configDNS_SERVER_ADDR1 	67
#define configDNS_SERVER_ADDR2 	222
#define configDNS_SERVER_ADDR3 	222


extern uint8_t ucMACAddress[ 6 ] ;
extern uint8_t ucIPAddress[ 4 ]; 
extern uint8_t ucNetMask[ 4 ]; 
extern uint8_t ucGatewayAddress[ 4 ]; 
extern uint8_t ucDNSServerAddress[ 4 ] ; 

#endif
