# FreeRTOS Third Party Ports

FreeRTOS third party ports can be supported by the FreeRTOS team, a FreeRTOS
partner or FreeRTOS community members. Depending on who supports it, the support
provided will differ as follows:

## FreeRTOS Team Supported Third Party FreeRTOS Ports

Location: https://github.com/FreeRTOS/FreeRTOS-Kernel/tree/main/portable/ThirdParty

These third party FreeRTOS ports are supported by the FreeRTOS team. For a
FreeRTOS team supported third party FreeRTOS port:

* The code has been reviewed by the FreeRTOS team.
* FreeRTOS team has access to the hardware and the test results have been
  verified by the FreeRTOS team.
* Customer queries as well as bugs are addressed by the FreeRTOS team.
* The code can be included in Long Term Support (LTS) releases.

A new FreeRTOS port cannot be directly contributed to this location. Instead,
the FreeRTOS team will decide to take ownership of a partner supported or a
community supported FreeRTOS port based on the community interest.

## Partner Supported FreeRTOS Ports

Location: https://github.com/FreeRTOS/FreeRTOS-Kernel-Partner-Supported-Ports/tree/main

These FreeRTOS ports are supported by a FreeRTOS partner. For a partner
supported FreeRTOS port:

* The code has not been reviewed by the FreeRTOS team.
* FreeRTOS team has not verified the tests results but tests exist and are
  reported to be successful by the partner.
* Customer queries as well as bugs are addressed by the partner.

A new FreeRTOS port can be directly contributed by a partner. The process to
contribute a FreeRTOS port is documented [here](https://github.com/FreeRTOS/FreeRTOS-Kernel-Partner-Supported-Ports/blob/main/README.md).

## Community Supported FreeRTOS Ports

Location: https://github.com/FreeRTOS/FreeRTOS-Kernel-Community-Supported-Ports/tree/main

These FreeRTOS ports are supported by the FreeRTOS community members. For a
community supported FreeRTOS port:

* The code has not been reviewed by the FreeRTOS team.
* Tests may or may not exist for the FreeRTOS port.
* Customer queries as well as bugs are addressed by the community.

A new FreeRTOS port can be directly contributed by anyone. The process to
contribute a FreeRTOS port is documented [here](https://github.com/FreeRTOS/FreeRTOS-Kernel-Community-Supported-Ports/blob/main/README.md).
