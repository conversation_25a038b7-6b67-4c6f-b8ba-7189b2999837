# Contribution guidelines

Thank you for your interest in contributing to our project. Whether it's a bug report, new feature, code, or
documentation, we welcome our community to be involved in this project.

Please read through this document before submitting any issues or pull requests to ensure we are able to help you and all members of the community as effectively as possible.

## Code of conduct
This project has adopted the [Amazon Open Source Code of Conduct](https://aws.github.io/code-of-conduct).
For more information see the [Code of Conduct FAQ](https://aws.github.io/code-of-conduct-faq) or contact
<EMAIL> with any additional questions or comments.


## Security issue notifications
If you discover a potential security issue in this project we ask that you notify AWS/Amazon Security via our [vulnerability reporting page](http://aws.amazon.com/security/vulnerability-reporting/). Please do **not** create a public github issue.


## Submitting a bugs/feature request
Have a bug to report or feature to request? Follow these steps:
1. Search on the [FreeRTOS Community Support Forums](https://forums.freertos.org/) and [GitHub issue tracker](https://github.com/FreeRTOS/FreeRTOS/issues?utf8=%E2%9C%93&q=is%3Aissue) to be sure this hasn't been already reported or discussed.
2. If your search turns up empty, create a new topic in the [forums](https://forums.freertos.org/) and work with the community to help clarify issues or refine the idea. Include as many of the details listed below.
3. Once the community has had time to discuss and digest, we welcome you to create an [issue](https://github.com/FreeRTOS/FreeRTOS/issues) to report bugs or suggest features.

When creating a new topic on the forums or filing an issue, please include as many relevant details as possible. Examples include:

* A clear description of the situation — what you observe, what you expect, and your view on how the two differ.
* A reproducible test case or sequence of steps.
* The version of our code being used.
* Any modifications you've made relevant to the bug.
* Details of your environment or deployment. Highlight anything unusual.


## Contributing via pull request
Contributions via pull requests are much appreciated. Before sending us a pull request, please ensure that:

1. You are working against the latest source on the *master* branch.
2. You check existing open, and recently merged, pull requests to make sure someone else hasn't addressed the problem already.
3. You open an issue to discuss any significant work - we would hate for your time to be wasted.

To send us a pull request, please:

1. Fork the repository.
2. Modify the source; focus on the specific change you are contributing. If you also reformat all the code, it will be hard for us to focus on your change.
3. Follow the [coding style guide](https://www.freertos.org/FreeRTOS-Coding-Standard-and-Style-Guide.html).
4. Commit to your fork using clear commit messages.
5. Send us a pull request, answering any default questions in the pull request interface.
   NOTE: Please make sure the default option (Allow edits from maintainers) is left checked.
6. Pay attention to any automated CI failures reported in the pull request, and stay involved in the conversation.

GitHub provides additional document on [forking a repository](https://help.github.com/articles/fork-a-repo/) and
[creating a pull request](https://help.github.com/articles/creating-a-pull-request/).

## Coding style
* Please ensure that your code complies to the [FreeRTOS coding style guidelines](https://www.freertos.org/FreeRTOS-Coding-Standard-and-Style-Guide.html).


## Getting your pull request merged
All pull requests must be approved by our review team before it can be merged in. We appreciate your patience while pull requests are reviewed. The time it takes to review will depend on complexity and consideration of wider implications.


## Finding contributions to work on
Looking at the existing issues is a great way to find something to contribute on. As our projects, by default, use the default GitHub issue labels (enhancement/bug/duplicate/help wanted/invalid/question/wontfix), tackling open 'help wanted' issues is a great place to start.


## Licensing
The FreeRTOS kernel is released under the MIT open source license, the text of which can be found [here](https://github.com/FreeRTOS/FreeRTOS/blob/master/FreeRTOS/License/license.txt)

Additional license files can be found in the folders containing any supplementary libraries licensed by their respective copyright owners where applicable.

We may ask you to sign a [Contributor License Agreement (CLA)](http://en.wikipedia.org/wiki/Contributor_License_Agreement) for larger changes.
