# GD32F4xx + LAN8720A 外部50MHz时钟配置说明

## 🔧 硬件连接配置

### 时钟连接方式
```
外部50MHz时钟源
    ├── LAN8720A XTAL1(CLKIN) 引脚  ← PHY芯片时钟输入
    └── GD32F4xx PA1(RMII_REF_CLK) ← RMII接口参考时钟
```

### RMII接口引脚分配
| 信号名称 | GD32F4xx引脚 | LAN8720A引脚 | 方向 | 说明 |
|---------|-------------|-------------|------|------|
| RMII_REF_CLK | PA1 | REF_CLK | 输入 | 50MHz参考时钟 |
| RMII_MDIO | PA2 | MDIO | 双向 | 管理数据接口 |
| RMII_CRS_DV | PA7 | CRS_DV | 输入 | 载波侦听/数据有效 |
| RMII_TX_EN | PB11 | TX_EN | 输出 | 发送使能 |
| RMII_TXD0 | PB12 | TXD0 | 输出 | 发送数据位0 |
| RMII_TXD1 | PB13 | TXD1 | 输出 | 发送数据位1 |
| RMII_MDC | PC1 | MDC | 输出 | 管理数据时钟 |
| RMII_RXD0 | PC4 | RXD0 | 输入 | 接收数据位0 |
| RMII_RXD1 | PC5 | RXD1 | 输入 | 接收数据位1 |

## ⚙️ 软件配置修改

### 主要修改点

1. **禁用MCU时钟输出**
   - 注释掉 `rcu_ckout0_config()` 相关配置
   - 不配置PA8(CK_OUT0)引脚

2. **RMII_REF_CLK配置为输入**
   - PA1配置为AF模式，接收外部50MHz时钟
   - 使用GPIO_AF_11复用功能

3. **添加外部时钟配置函数**
   - `enet_external_clock_config()` 函数
   - 包含配置验证和调试信息

### 代码修改示例

```c
/* 原始代码 - 使用MCU输出时钟 */
// rcu_ckout0_config(RCU_CKOUT0SRC_HXTAL, RCU_CKOUT0_DIV1); // 25M

/* 修改后 - 使用外部时钟 */
// 注释掉MCU时钟输出配置
/* RMII_REF_CLK (PA1) 配置为输入模式，接收外部50MHz时钟 */
gpio_mode_set(RMII_REF_CLK_GPIO_NUM, GPIO_MODE_AF, GPIO_PUPD_NONE, RMII_REF_CLK_PIN);
gpio_af_set(RMII_REF_CLK_GPIO_NUM, GPIO_AF_11, RMII_REF_CLK_PIN);
```

## 🔍 验证方法

### 1. 硬件验证
- 使用示波器测量PA1引脚的50MHz时钟信号
- 检查时钟信号质量：上升/下降时间 < 2ns
- 验证时钟占空比接近50%

### 2. 软件验证
- 串口输出："Using external 50MHz clock for LAN8720A and RMII interface"
- 网络初始化成功："enet_init ok"
- IP地址配置成功显示

### 3. 网络连通性测试
- Ping测试
- TCP/UDP通信测试
- 网络性能测试

## ⚠️ 注意事项

1. **时钟信号质量**
   - 确保50MHz时钟信号稳定，抖动小于±50ppm
   - 时钟驱动能力足够，能同时驱动PHY和MCU

2. **PCB布线**
   - 时钟信号走线尽量短，避免过孔
   - 时钟线与其他信号线保持适当距离
   - 在时钟线下方铺地平面

3. **电源去耦**
   - 在LAN8720A和GD32F4xx附近放置去耦电容
   - 确保电源纹波小于50mV

4. **调试建议**
   - 如果网络不通，首先检查时钟信号
   - 使用MDIO接口读取PHY寄存器状态
   - 检查链路状态LED指示

## 📊 性能预期

使用外部50MHz时钟源的优势：
- ✅ 时钟精度更高，网络稳定性更好
- ✅ 减少MCU时钟树负载
- ✅ 降低EMI干扰
- ✅ 支持更高的网络传输速率

预期网络性能：
- 10/100Mbps自适应
- TCP吞吐量：>80Mbps
- 延迟：<1ms
- 丢包率：<0.01%
