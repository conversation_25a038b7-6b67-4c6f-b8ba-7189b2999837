#include "main.h"
#include "FreeRTOSConfig.h"
#include "FreeRTOS_IP.h"
#include "trng.h"
#include "enet.h"
TaskHandle_t xEMACTaskHandle = NULL; /* 保存用作延迟中断处理器的任务句柄。使用句柄可以将所有EMAC/DMA相关中断的直接通知发送到任务。 */
QueueHandle_t xPingReplyQueue = NULL;
void eth_rece_data_task( void *pvParameters );
void ethernet_task_creation(void)/*创建以太网任务*/
{
    printf("Ethernet is negotiating \r\n");
    BaseType_t xReturn;
    xPingReplyQueue = xQueueCreate( 20, sizeof( uint16_t ) );             /*Ping创建应答队列*/
    xReturn=xTaskCreate( eth_rece_data_task, "eth_rece_data_task", 640, NULL, 31, &xEMACTaskHandle );//以太网接口接收数据任务,不需要它直接做什么
    if(xReturn==errCOULD_NOT_ALLOCATE_REQUIRED_MEMORY )
        printf("eth_rece_data_task errCOULD_NOT_ALLOCATE_REQUIRED_MEMORY\r\n");  
}

/*协议栈初始化，会调用xNetworkInterfaceInitialise来初始化硬件*/
BaseType_t xNetworkInterfaceInitialise( void )
{
	if(InitialiseNetwork()) 
    {
        printf("enet_init ok\r\n");
        return pdPASS ;
    }  
    return pdFAIL ;
}

/*以太网中断服务函数*/
void ENET_IRQHandler(void)
{
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    /* 帧收到 */
    if(SET == enet_interrupt_flag_get(ENET_DMA_INT_FLAG_RS)) 
    {
        /* 唤醒 接收任务 。 */
        if( xEMACTaskHandle != NULL )
        {
            vTaskNotifyGiveFromISR( xEMACTaskHandle, &xHigherPriorityTaskWoken );//发送通知
            portYIELD_FROM_ISR( xHigherPriorityTaskWoken );
        }
    }
    /* 清除enet DMA Rx中断挂起位 */
    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_RS_CLR);
    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_NI_CLR);

    /* 必要时切换任务 */
    if(pdFALSE != xHigherPriorityTaskWoken) {
        portEND_SWITCHING_ISR(xHigherPriorityTaskWoken);
    }
}



/*网络接口输出*/
BaseType_t xNetworkInterfaceOutput( NetworkBufferDescriptor_t * const pxDescriptor,BaseType_t xReleaseAfterSend )
{
    enet_frame_transmit( pxDescriptor->pucEthernetBuffer, pxDescriptor->xDataLength );
    /*调用标准跟踪宏来记录发送事件。*/
    iptraceNETWORK_INTERFACE_TRANSMIT();

    if( xReleaseAfterSend != pdFALSE )
    {   
        vReleaseNetworkBufferAndDescriptor( pxDescriptor );
    }
    return pdTRUE;
}

/*简单的接收处理程序示例*/
/*延迟中断处理程序是一个标准的RTOS任务。FreeRTOS的集中延迟中断处理能力也可以使用。*/
/*GD32的描述符结构体：enet_descriptors_struct */
void eth_rece_data_task( void *pvParameters )
{
    NetworkBufferDescriptor_t *pxBufferDescriptor;//网络缓冲区描述符
    size_t xBytesReceived;                          //接收字节数
    IPStackEvent_t xRxEvent;    /* 用于指示由于以太网接收事件而调用 xSendEventStructToIPTask() 。 */

    while(1)
    {
        /* 等待以太网MAC中断来表明已经接收到另一个数据包。
        任务通知的使用方式与计数信号量类似，用于计数Rx事件，但比信号量更有效。*/
        ulTaskNotifyTake( pdFALSE, portMAX_DELAY );
        
        xBytesReceived = enet_rxframe_size_get();/*查看接收了多少数据*/

        if( xBytesReceived > 0 )
        {
            /*分配一个网络缓冲区描述符，该描述符指向一个足够大的缓冲区，以容纳接收到的帧。
            由于这是一个简单而不是有效的例子，接收到的数据将仅被复制到这个缓冲区中。 */
            pxBufferDescriptor = pxGetNetworkBufferWithDescriptor( xBytesReceived, 0 );

            if( pxBufferDescriptor != NULL )//表示以获取网络缓冲区的描述符的指针
            {
                /*pxBufferDescriptor->pucEthernetBuffer 现在指向一个足够大的以太网缓冲区，用来容纳接收到的数据。
                将接收到的数据复制到 pcNetworkBuffer->pucEthernetBuffer中 */
                enet_frame_receive( pxBufferDescriptor->pucEthernetBuffer ,xBytesReceived);//将接收到的数据作为函数参数传入的缓冲区中。
                pxBufferDescriptor->xDataLength = xBytesReceived;

                /*查看是否需要处理接收到的以太网帧中的数据。
                注意!最好是在中断服务例程本身中执行此操作，这样就不用为无需处理的数据包解除阻塞任务。 */
                if( eConsiderFrameForProcessing( pxBufferDescriptor->pucEthernetBuffer )== eProcessBuffer )
                { 
                    xRxEvent.eEventType = eNetworkRxEvent;/* 即将被发送到TCP/IP的事件是Rx事件。*/  
                    xRxEvent.pvData = ( void * ) pxBufferDescriptor; /* pvData用于指向现在引用接收到的数据的网络缓冲区描述符。*/
 
                    if( xSendEventStructToIPTask( &xRxEvent, 0 ) == pdFALSE )/* 将数据发送到TCP/IP堆栈中。 */
                    {                       
                        vReleaseNetworkBufferAndDescriptor( pxBufferDescriptor );/* 缓冲区无法发送到IP任务，因此必须释放缓冲区。 */ 
                        iptraceETHERNET_RX_EVENT_LOST(); /*  调用标准跟踪宏来记录发生的情况。*/
                    }
                    else
                    {                 
                        iptraceNETWORK_INTERFACE_RECEIVE();  /* 日志含义成功发送消息到TCP/IP栈。调用标准跟踪宏来记录事件的发生。 */
                    } 
                }
                else
                {
                    vReleaseNetworkBufferAndDescriptor( pxBufferDescriptor );/*可以丢弃以太网帧，但是必须释放以太网缓冲区。*/
                }
            }
            else
            {       
                iptraceETHERNET_RX_EVENT_LOST();/* 事件丢失，因为网络缓冲区不可用。调用标准跟踪宏来记录事件的发生。*/
				printf("eth buf error\r\n");
            }
        }
    }
}
void vApplicationIPNetworkEventHook( eIPCallbackEvent_t eNetworkEvent )
{
    uint32_t ulIPAddress, ulNetMask, ulGatewayAddress, ulDNSServerAddress;
    static BaseType_t xTasksAlreadyCreated = pdFALSE;//已创建的任务
    char cBuffer[ 16 ];
    /*检查这是网络启动，而不是网络关闭。*/
    if( eNetworkEvent == eNetworkUp )   //eNetworkUp：如果网络配置完成
    {
        /* 如果尚未创建使用TCP/IP堆栈的任务，则创建这些任务。 */
        if( xTasksAlreadyCreated == pdFALSE )   //注意，此处是协议栈初始化成功，并不代表已经连接上网络
        {   /*在这里创建任务*/
            
            ethernet_task_creation();/*创建以太网任务*/
            xTasksAlreadyCreated = pdTRUE;
        }
        else if( xTasksAlreadyCreated == pdTRUE ) 
        {
            printf("xTasksAlreadyCreated == pdTRUE\r\n");
        }

        /* 网络已启动并配置完毕。打印配置，该配置可能是从DHCP服务器获得的。 */
        FreeRTOS_GetAddressConfiguration( &ulIPAddress, &ulNetMask, &ulGatewayAddress, &ulDNSServerAddress );
    
        /*将IP地址转换为字符串，然后打印出来。 */
        FreeRTOS_inet_ntoa( ulIPAddress, cBuffer );//将ip地址的32位表示转换为点分十进制。
        printf( "IP Address: %s\r\n", cBuffer );
        
        /* 将网关的IP地址转换为字符串，然后打印出来。 */
        FreeRTOS_inet_ntoa( ulGatewayAddress, cBuffer );
        printf( "Gateway IP: %s\r\n", cBuffer );

        /* 将子网掩码转换为字符串，然后打印出来。 */
        FreeRTOS_inet_ntoa( ulNetMask, cBuffer );
        printf( "Subnet Mask: %s\r\n", cBuffer );
    }
    else if ( eNetworkEvent == eNetworkDown)//网络断开，但并非所有驱动程序都能实现此功能
    { 
        printf("Disconnected from the network\r\n");
    }
}

/*日志打印*/
void vLoggingPrintf( const char *pcFormatString, ... )
{
    
}
/*获取随机数*/
BaseType_t xApplicationGetRandomNumber( uint32_t * pulNumber )
{
    *(pulNumber) = uxRand();
    return pdTRUE;
}

/*用于 检查接收到的 LLMNR 或 NBNS 名称是否与正在查找的设备相同。*/
/*DNS或LLMNR请求的查询回调*/
BaseType_t xApplicationDNSQueryHook( const char *pcName )
{  
    return pdPASS; 
} 

/*为每个RFC生成一个32位的随机TCP初始序列号。*/
uint32_t ulApplicationGetNextSequenceNumber( uint32_t ulSourceAddress, uint16_t usSourcePort,uint32_t ulDestinationAddress, uint16_t usDestinationPort )
{
    ( void ) ulSourceAddress;
    ( void ) usSourcePort;
    ( void ) ulDestinationAddress;
    ( void ) usDestinationPort;
    return uxRand();
}

UBaseType_t uxRand(void)//typedef unsigned long    UBaseType_t
{ 
    return (UBaseType_t) trng_random_range_get(); 
} 
