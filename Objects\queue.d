.\objects\queue.o: Middlewares\FreeRTOS_Kernel\queue.c
.\objects\queue.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\queue.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
.\objects\queue.o: .\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h
.\objects\queue.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\queue.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\queue.o: .\user\FreeRTOSConfig.h
.\objects\queue.o: .\CMSIS\gd32f4xx.h
.\objects\queue.o: .\CMSIS\core_cm4.h
.\objects\queue.o: .\CMSIS\core_cmInstr.h
.\objects\queue.o: .\CMSIS\core_cmFunc.h
.\objects\queue.o: .\CMSIS\core_cm4_simd.h
.\objects\queue.o: .\CMSIS\system_gd32f4xx.h
.\objects\queue.o: .\CMSIS\gd32f4xx_libopt.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_rcu.h
.\objects\queue.o: .\CMSIS\gd32f4xx.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_adc.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_can.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_crc.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_ctc.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_dac.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_dbg.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_dci.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_dma.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_exti.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_fmc.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_fwdgt.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_gpio.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_syscfg.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_i2c.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_iref.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_pmu.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_rtc.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_sdio.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_spi.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_timer.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_trng.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_usart.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_wwdgt.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_misc.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_enet.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_exmc.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_ipa.h
.\objects\queue.o: .\FWLib\Include\gd32f4xx_tli.h
.\objects\queue.o: .\Middlewares\FreeRTOS_Kernel\include\projdefs.h
.\objects\queue.o: .\Middlewares\FreeRTOS_Kernel\include\portable.h
.\objects\queue.o: .\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h
.\objects\queue.o: .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\queue.o: .\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h
.\objects\queue.o: .\Middlewares\FreeRTOS_Kernel\include\task.h
.\objects\queue.o: .\Middlewares\FreeRTOS_Kernel\include\list.h
.\objects\queue.o: .\Middlewares\FreeRTOS_Kernel\include\queue.h
