# LAN8720A PHY地址修复说明

## 🎯 问题解决

### 发现的问题
- **PHY芯片地址配置错误**：代码中设置为地址0，但硬件实际为地址1
- **LAN8720A正常工作**：芯片ID检测正确 (ID1=0x0007, ID2=0xC0F1)

### 已完成的修复
1. ✅ **更新PHY地址配置**
   - 文件：`FWLib/Include/gd32f4xx_enet.h`
   - 修改：`#define PHY_ADDRESS ((uint16_t)1U)`

2. ✅ **增强调试信息**
   - 添加PHY地址自动检测功能
   - 显示检测到的PHY地址信息

## 🚀 预期结果

重新编译并下载代码后，应该看到：

```
=== PHY Status Check ===
Trying PHY address 0...
Trying PHY address 1...
PHY found at address 1, ID1: 0x0007
PHY ID2: 0xC0F1
LAN8720A detected at address 1!
NOTE: PHY_ADDRESS is now set to 1
Using configured PHY address 1 for detailed check...
PHY BCR: 0x3100  (正常值，不再是0xFFFF)
PHY BSR: 0x7849  (正常值，显示真实的链路状态)
```

## 📋 下一步测试

1. **编译代码**
2. **下载到MCU**
3. **检查串口输出**
4. **验证网络连接**

如果PHY通信正常，应该能看到：
- `enet_init ok` 消息
- 正确的IP地址配置信息
- 网络连接成功

## 🔧 硬件配置确认

您的LAN8720A硬件配置：
- **PHY地址**: 1 (PHYAD0引脚配置)
- **接口模式**: RMII
- **时钟源**: 外部50MHz
- **芯片ID**: 正确 (0x0007, 0xC0F1)

## ⚠️ 注意事项

如果仍有问题，可能需要检查：
1. **网线连接**
2. **网络交换机/路由器**
3. **PHY芯片的链路状态LED**
4. **网络配置参数**（IP地址、子网掩码等）
