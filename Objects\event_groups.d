.\objects\event_groups.o: Middlewares\FreeRTOS_Kernel\event_groups.c
.\objects\event_groups.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\event_groups.o: .\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h
.\objects\event_groups.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\event_groups.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\event_groups.o: .\user\FreeRTOSConfig.h
.\objects\event_groups.o: .\CMSIS\gd32f4xx.h
.\objects\event_groups.o: .\CMSIS\core_cm4.h
.\objects\event_groups.o: .\CMSIS\core_cmInstr.h
.\objects\event_groups.o: .\CMSIS\core_cmFunc.h
.\objects\event_groups.o: .\CMSIS\core_cm4_simd.h
.\objects\event_groups.o: .\CMSIS\system_gd32f4xx.h
.\objects\event_groups.o: .\CMSIS\gd32f4xx_libopt.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_rcu.h
.\objects\event_groups.o: .\CMSIS\gd32f4xx.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_adc.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_can.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_crc.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_ctc.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_dac.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_dbg.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_dci.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_dma.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_exti.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_fmc.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_fwdgt.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_gpio.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_syscfg.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_i2c.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_iref.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_pmu.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_rtc.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_sdio.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_spi.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_timer.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_trng.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_usart.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_wwdgt.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_misc.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_enet.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_exmc.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_ipa.h
.\objects\event_groups.o: .\FWLib\Include\gd32f4xx_tli.h
.\objects\event_groups.o: .\Middlewares\FreeRTOS_Kernel\include\projdefs.h
.\objects\event_groups.o: .\Middlewares\FreeRTOS_Kernel\include\portable.h
.\objects\event_groups.o: .\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h
.\objects\event_groups.o: .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\event_groups.o: .\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h
.\objects\event_groups.o: .\Middlewares\FreeRTOS_Kernel\include\task.h
.\objects\event_groups.o: .\Middlewares\FreeRTOS_Kernel\include\list.h
.\objects\event_groups.o: .\Middlewares\FreeRTOS_Kernel\include\timers.h
.\objects\event_groups.o: .\Middlewares\FreeRTOS_Kernel\include\event_groups.h
