.\objects\tasks.o: Middlewares\FreeRTOS_Kernel\tasks.c
.\objects\tasks.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\tasks.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
.\objects\tasks.o: .\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h
.\objects\tasks.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\tasks.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\tasks.o: .\user\FreeRTOSConfig.h
.\objects\tasks.o: .\CMSIS\gd32f4xx.h
.\objects\tasks.o: .\CMSIS\core_cm4.h
.\objects\tasks.o: .\CMSIS\core_cmInstr.h
.\objects\tasks.o: .\CMSIS\core_cmFunc.h
.\objects\tasks.o: .\CMSIS\core_cm4_simd.h
.\objects\tasks.o: .\CMSIS\system_gd32f4xx.h
.\objects\tasks.o: .\CMSIS\gd32f4xx_libopt.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_rcu.h
.\objects\tasks.o: .\CMSIS\gd32f4xx.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_adc.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_can.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_crc.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_ctc.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_dac.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_dbg.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_dci.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_dma.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_exti.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_fmc.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_fwdgt.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_gpio.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_syscfg.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_i2c.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_iref.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_pmu.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_rtc.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_sdio.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_spi.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_timer.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_trng.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_usart.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_wwdgt.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_misc.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_enet.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_exmc.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_ipa.h
.\objects\tasks.o: .\FWLib\Include\gd32f4xx_tli.h
.\objects\tasks.o: .\Middlewares\FreeRTOS_Kernel\include\projdefs.h
.\objects\tasks.o: .\Middlewares\FreeRTOS_Kernel\include\portable.h
.\objects\tasks.o: .\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h
.\objects\tasks.o: .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\tasks.o: .\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h
.\objects\tasks.o: .\Middlewares\FreeRTOS_Kernel\include\task.h
.\objects\tasks.o: .\Middlewares\FreeRTOS_Kernel\include\list.h
.\objects\tasks.o: .\Middlewares\FreeRTOS_Kernel\include\timers.h
.\objects\tasks.o: .\Middlewares\FreeRTOS_Kernel\include\stack_macros.h
